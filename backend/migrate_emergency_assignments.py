#!/usr/bin/env python3
"""
Add assignment fields to emergencies table for automatic assignment functionality
"""

from database import get_db
from sqlalchemy import text
import sys

def migrate_emergency_assignments():
    """Add assignment fields to emergencies table"""
    print("🔄 Adding assignment fields to emergencies table...")
    
    db = next(get_db())
    
    try:
        # Add assignment fields to emergencies table
        migration_queries = [
            "ALTER TABLE emergencies ADD COLUMN assigned_team_id INTEGER REFERENCES teams(id);",
            "ALTER TABLE emergencies ADD COLUMN assigned_driver_id TEXT REFERENCES users(id);",
            "ALTER TABLE emergencies ADD COLUMN assigned_vehicle_id INTEGER REFERENCES vehicles(id);"
        ]
        
        for query in migration_queries:
            try:
                db.execute(text(query))
                print(f"✅ Executed: {query}")
            except Exception as e:
                if "duplicate column name" in str(e).lower() or "already exists" in str(e).lower():
                    print(f"⚠️  Column already exists: {query}")
                else:
                    print(f"❌ Error executing {query}: {e}")
                    raise
        
        db.commit()
        print("✅ Emergency assignment fields migration completed successfully!")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        db.rollback()
        sys.exit(1)
    finally:
        db.close()

if __name__ == "__main__":
    migrate_emergency_assignments()
