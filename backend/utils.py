from models import UrgencyLevel, FireStation, Vehicle, VehicleStatus
from sqlalchemy.orm import Session
from typing import Optional
from geopy.distance import geodesic
import random
import string
import sib_api_v3_sdk
from sib_api_v3_sdk.rest import ApiException
import os

def calculate_urgency(smoke_level: float) -> UrgencyLevel:
    if smoke_level >= 5000:
        return UrgencyLevel.HIGH
    elif smoke_level >= 3500:
        return UrgencyLevel.MEDIUM
    else:
        return UrgencyLevel.LOW

def find_nearest_station_with_vehicles(latitude: float, longitude: float, db: Session) -> Optional[FireStation]:
    stations = db.query(FireStation).filter(FireStation.is_active == True).all()
    attempts = 0
    checked_station_ids = set()
    while attempts < 3:
        available_stations = []
        for station in stations:
            if station.id in checked_station_ids:
                continue
            available_vehicles = db.query(Vehicle).filter(
                Vehicle.station_id == station.id,
                Vehicle.status == VehicleStatus.AVAILABLE
            ).count()
            if available_vehicles > 0:
                distance = geodesic((latitude, longitude), (station.latitude, station.longitude)).kilometers
                available_stations.append((station, distance))
        if available_stations:
            return min(available_stations, key=lambda x: x[1])[0]
        # Mark all checked this round
        checked_station_ids.update([s.id for s, _ in available_stations])
        attempts += 1
    # If no available vehicles after 3 tries, just return the closest station (even if no vehicles)
    if stations:
        stations_with_distance = [(station, geodesic((latitude, longitude), (station.latitude, station.longitude)).kilometers) for station in stations]
        return min(stations_with_distance, key=lambda x: x[1])[0]
    return None

def generate_verification_code() -> str:
    return ''.join(random.choices(string.digits, k=6))

def format_phone_number(phone: str) -> str:
    """Format phone number to Malawi standard"""
    phone = phone.replace(' ', '').replace('-', '')
    if phone.startswith('0'):
        phone = '+265' + phone[1:]
    elif not phone.startswith('+265'):
        phone = '+265' + phone
    return phone

def generate_random_password(length: int = 10) -> str:
    chars = string.ascii_letters + string.digits
    return ''.join(random.choices(chars, k=length))

def send_email_brevo(to_email: str, to_name: str, subject: str, html_content: str):
    api_key = os.getenv('BREVO_API_KEY')
    if not api_key:
        raise Exception('BREVO_API_KEY not set in environment variables')
    configuration = sib_api_v3_sdk.Configuration()
    configuration.api_key['api-key'] = api_key
    api_instance = sib_api_v3_sdk.TransactionalEmailsApi(sib_api_v3_sdk.ApiClient(configuration))
    sender = {"name": "MyGuardian+", "email": os.getenv('BREVO_SENDER_EMAIL', '<EMAIL>')}
    to = [{"email": to_email, "name": to_name}]
    send_smtp_email = sib_api_v3_sdk.SendSmtpEmail(
        to=to,
        sender=sender,
        subject=subject,
        html_content=html_content
    )
    try:
        api_instance.send_transac_email(send_smtp_email)
    except ApiException as e:
        print(f"Exception when sending email: {e}")