import requests
import random
import time

# M<PERSON><PERSON>, Malawi bounding box (approximate)
LAT_MIN, LAT_MAX = -11.5000, -11.4000
LON_MIN, LON_MAX = 33.9500, 34.0500

BACKEND_URL = "http://localhost:8000/device/data"  # Adjust if needed

MAC_ADDRESSES = [
    "BC:A8:A6:1B:61:37",
    "BC:A8:A6:1B:61:37",
    "BC:A8:A6:1B:61:37",
]

def random_location():
    lat = random.uniform(LAT_MIN, LAT_MAX)
    lon = random.uniform(LON_MIN, LON_MAX)
    return lat, lon

def random_mac():
    return random.choice(MAC_ADDRESSES)

def random_smoke_level():
    return round(random.uniform(0, 4000), 2)

def send_device_data():
    lat, lon = random_location()
    mac = random_mac()
    smoke = random_smoke_level()
    payload = {
        "mac_address": mac,
        "latitude": -11.499030248354636, 
        "longitude": 33.97292476996564,
        "smoke_level": smoke,
    }
    try:
        resp = requests.post(BACKEND_URL, json=payload)
        print(f"Sent: {payload} | Status: {resp.status_code}")
    except Exception as e:
        print(f"Error sending data: {e}")

def main():
    while True:
        send_device_data()
        time.sleep(random.uniform(1, 5))  # Send every 1-5 seconds

if __name__ == "__main__":
    main()
