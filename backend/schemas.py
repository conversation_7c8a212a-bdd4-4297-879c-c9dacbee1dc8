from pydantic import BaseModel, <PERSON>, validator
from typing import List, Optional
from datetime import datetime
from models import UserRole, UrgencyLevel, EmergencyStatus, VehicleStatus
import re

class UserCreate(BaseModel):
    email: str
    full_name: str
    password: Optional[str] = None  # Optional since backend generates random password
    role: UserRole
    phone_number: str
    station_id: Optional[int] = None
    vehicle_id: Optional[int] = None  # Assigned vehicle

class MobileUserRegister(BaseModel):
    email: str
    full_name: str
    password: str
    phone_number: str
    
    @validator('email')
    def validate_email(cls, v):
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError('Invalid email format')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 6:  # Simplified password requirement
            raise ValueError('Password must be at least 6 characters long')
        return v

class UserResponse(BaseModel):
    id: str  # String for UUID
    email: str
    full_name: str
    role: UserRole
    phone_number: Optional[str] = None
    is_active: bool
    is_verified: bool
    station_id: Optional[int]
    vehicle_id: Optional[int] = None
    
    class Config:
        from_attributes = True

class VehicleResponse(BaseModel):
    id: int
    vehicle_number: str
    vehicle_type: str
    status: VehicleStatus
    station_id: int
    
    class Config:
        from_attributes = True

class FireStationCreate(BaseModel):
    name: str
    latitude: float
    longitude: float
    region: str
    district: str
    address: str
    phone_number: str
    manager_email: Optional[str] = None
    manager_name: Optional[str] = None
    manager_password: Optional[str] = None
    manager_phone: Optional[str] = None

class FireStationResponse(BaseModel):
    id: int
    name: str
    latitude: float
    longitude: float
    region: str
    district: str
    address: str
    phone_number: str
    is_active: bool
    managers: Optional[List[UserResponse]] = None
    vehicles: Optional[List[VehicleResponse]] = None
    
    class Config:
        from_attributes = True

class VehicleCreate(BaseModel):
    vehicle_number: str
    vehicle_type: str
    station_id: int

class VehicleUpdate(BaseModel):
    vehicle_number: str
    vehicle_type: str
    status: VehicleStatus
    station_id: int
    # Add more fields as needed for update

class DevicePairRequest(BaseModel):
    mac_address: str
    device_name: str
    user_id: str

class SensorData(BaseModel):
    mac_address: str
    latitude: float
    longitude: float
    smoke_level: float

class SensorReadingResponse(BaseModel):
    id: int
    mac_address: str
    latitude: float
    longitude: float
    smoke_level: float
    timestamp: datetime
    
    class Config:
        from_attributes = True

class EmergencyResponse(BaseModel):
    id: int
    latitude: float
    longitude: float
    smoke_level: float
    urgency_level: UrgencyLevel
    status: EmergencyStatus
    created_at: datetime
    resolved_at: Optional[datetime]
    station_id: Optional[int] = None
    
    class Config:
        from_attributes = True

class LoginRequest(BaseModel):
    email: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str
    user: UserResponse

class DispatchRequest(BaseModel):
    emergency_id: int
    vehicle_ids: List[int]

class AssignResponderVehicleRequest(BaseModel):
    responder_id: str
    vehicle_id: int

class ChangeResponderVehicleRequest(BaseModel):
    responder_id: str
    new_vehicle_id: int

class PasswordChangeRequest(BaseModel):
    current_password: str
    new_password: str

    @validator('new_password')
    def validate_new_password(cls, v):
        if len(v) < 8:
            raise ValueError('New password must be at least 8 characters long')
        return v

class UserProfileUpdate(BaseModel):
    full_name: Optional[str] = None
    phone_number: Optional[str] = None
    emergency_contact_name: Optional[str] = None
    emergency_contact_phone: Optional[str] = None
    medical_conditions: Optional[str] = None
    medications: Optional[str] = None

class FireResponseReport(BaseModel):
    emergency_id: int
    cause_determined: Optional[str] = None
    cause_description: Optional[str] = None
    water_used_liters: Optional[float] = None
    fuel_used_liters: Optional[float] = None
    personnel_count: Optional[int] = None
    response_duration_minutes: Optional[int] = None
    property_damage_estimate: Optional[float] = None
    injuries_count: Optional[int] = None
    fatalities_count: Optional[int] = None
    additional_notes: Optional[str] = None

class TeamReassignmentRequest(BaseModel):
    user_id: int
    new_station_id: int
    reason: Optional[str] = None

class WaterRefillPointBase(BaseModel):
    name: Optional[str] = None
    latitude: float
    longitude: float
    description: Optional[str] = None

class WaterRefillPointCreate(WaterRefillPointBase):
    pass

class WaterRefillPointResponse(WaterRefillPointBase):
    id: int
    created_at: datetime
    class Config:
        from_attributes = True

# Team Management Schemas
class TeamCreate(BaseModel):
    name: str
    station_id: int

class TeamResponse(BaseModel):
    id: int
    name: str
    station_id: int
    created_at: datetime
    is_active: bool

    class Config:
        from_attributes = True

class TeamMemberAdd(BaseModel):
    team_id: int
    user_id: str
    role: Optional[str] = "member"

class TeamMemberResponse(BaseModel):
    id: int
    team_id: int
    user_id: str
    role: str
    joined_at: datetime
    is_active: bool
    user: Optional[UserResponse] = None

    class Config:
        from_attributes = True

# Duty Shift Schemas
class DutyShiftCreate(BaseModel):
    user_id: str
    team_id: Optional[int] = None
    vehicle_id: Optional[int] = None

class DutyShiftResponse(BaseModel):
    id: int
    user_id: str
    team_id: Optional[int]
    vehicle_id: Optional[int]
    shift_start: datetime
    shift_end: Optional[datetime]
    status: str
    user: Optional[UserResponse] = None

    class Config:
        from_attributes = True

# Vehicle Driver Schemas
class VehicleDriverAssign(BaseModel):
    vehicle_id: int
    driver_id: str

class VehicleDriverResponse(BaseModel):
    id: int
    vehicle_id: int
    driver_id: str
    assigned_at: datetime
    is_active: bool
    driver: Optional[UserResponse] = None

    class Config:
        from_attributes = True

class ClockInRequest(BaseModel):
    user_ids: List[str]
    team_id: Optional[int] = None
    vehicle_id: Optional[int] = None