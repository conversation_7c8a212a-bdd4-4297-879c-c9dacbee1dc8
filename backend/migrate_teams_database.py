#!/usr/bin/env python3
"""
Database migration script for team management features
Adds tables for teams, team members, duty shifts, and vehicle drivers
"""

from database import engine, get_db
from models import Base, User, UserRole, Vehicle, Team, TeamMember, DutyShift, VehicleDriver
from sqlalchemy import text, inspect
from sqlalchemy.orm import Session
import uuid

def check_table_exists(table_name):
    """Check if a table exists in the database"""
    inspector = inspect(engine)
    return table_name in inspector.get_table_names()

def migrate_database():
    """Run the database migration"""
    print("🔄 Starting team management database migration...")
    
    try:
        # Create all tables
        Base.metadata.create_all(bind=engine)
        print("✅ Database tables created/updated successfully")
        
        # Check which new tables were created
        new_tables = ['teams', 'team_members', 'duty_shifts', 'vehicle_drivers']
        for table in new_tables:
            if check_table_exists(table):
                print(f"✅ Table '{table}' is ready")
            else:
                print(f"❌ Table '{table}' was not created")
        
        # Create sample data
        create_sample_data()
        
        print("🎉 Team management migration completed successfully!")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        raise

def create_sample_data():
    """Create sample teams and drivers"""
    db = next(get_db())
    
    try:
        print("\n📝 Creating sample data...")
        
        # Create a sample driver user
        existing_driver = db.query(User).filter(User.role == UserRole.DRIVER).first()
        if not existing_driver:
            driver_id = str(uuid.uuid4())
            from auth import hash_password
            
            sample_driver = User(
                id=driver_id,
                email="<EMAIL>",
                full_name="John Driver",
                hashed_password=hash_password("driver123"),
                role=UserRole.DRIVER,
                phone_number="+1234567890",
                station_id=1,
                is_verified=True,
                has_changed_default_password=True
            )
            
            db.add(sample_driver)
            db.commit()
            print(f"✅ Created sample driver: {sample_driver.email}")
        else:
            print(f"✅ Driver user already exists: {existing_driver.email}")
        
        # Create a sample team
        existing_team = db.query(Team).first()
        if not existing_team:
            # Find a station manager to create the team
            station_manager = db.query(User).filter(User.role == UserRole.STATION_MANAGER).first()
            if station_manager:
                sample_team = Team(
                    name="Alpha Team",
                    station_id=station_manager.station_id,
                    created_by=station_manager.id
                )
                
                db.add(sample_team)
                db.commit()
                print(f"✅ Created sample team: {sample_team.name}")
                
                # Add responders to the team if any exist
                responders = db.query(User).filter(
                    User.role == UserRole.RESPONDER,
                    User.station_id == station_manager.station_id
                ).limit(3).all()
                
                for responder in responders:
                    team_member = TeamMember(
                        team_id=sample_team.id,
                        user_id=responder.id,
                        role="member"
                    )
                    db.add(team_member)
                
                db.commit()
                print(f"✅ Added {len(responders)} responders to the team")
            else:
                print("⚠️ No station manager found to create sample team")
        else:
            print(f"✅ Team already exists: {existing_team.name}")
        
        # Assign drivers to vehicles
        vehicles = db.query(Vehicle).all()
        drivers = db.query(User).filter(User.role == UserRole.DRIVER).all()
        
        if vehicles and drivers:
            for i, vehicle in enumerate(vehicles[:len(drivers)]):
                driver = drivers[i]
                
                # Check if assignment already exists
                existing_assignment = db.query(VehicleDriver).filter(
                    VehicleDriver.vehicle_id == vehicle.id,
                    VehicleDriver.driver_id == driver.id,
                    VehicleDriver.is_active == True
                ).first()
                
                if not existing_assignment:
                    vehicle_driver = VehicleDriver(
                        vehicle_id=vehicle.id,
                        driver_id=driver.id
                    )
                    db.add(vehicle_driver)
            
            db.commit()
            print(f"✅ Assigned drivers to vehicles")
        
        print("✅ Sample data creation completed")
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        db.rollback()
    finally:
        db.close()

def verify_migration():
    """Verify the migration was successful"""
    print("\n🔍 Verifying migration...")
    
    db = next(get_db())
    try:
        # Check teams
        teams_count = db.query(Team).count()
        print(f"✅ Teams in database: {teams_count}")
        
        # Check team members
        members_count = db.query(TeamMember).count()
        print(f"✅ Team members in database: {members_count}")
        
        # Check duty shifts
        shifts_count = db.query(DutyShift).count()
        print(f"✅ Duty shifts in database: {shifts_count}")
        
        # Check vehicle drivers
        drivers_count = db.query(VehicleDriver).count()
        print(f"✅ Vehicle driver assignments: {drivers_count}")
        
        # Check driver users
        driver_users_count = db.query(User).filter(User.role == UserRole.DRIVER).count()
        print(f"✅ Driver users in database: {driver_users_count}")
        
        print("✅ Migration verification completed successfully!")
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("Team Management Database Migration")
    print("=" * 50)
    
    try:
        migrate_database()
        verify_migration()
        
        print("\n🎉 All done! The team management features are now ready to use.")
        print("\nNew features available:")
        print("- Create and manage teams")
        print("- Assign responders to teams")
        print("- Clock in/out team members and drivers")
        print("- Assign drivers to vehicles")
        print("- View duty status and vehicle assignments")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        exit(1)
