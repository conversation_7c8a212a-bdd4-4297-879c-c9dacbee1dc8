# Python
__pycache__/
*.py[cod]
*.pyo
*.pyd
*.pyc
*.pyo
*.pyd
*.pdb
*.egg-info/
.eggs/
dist/
build/
*.egg
*.log
*.sqlite3
*.db
*.env
.env.*
.venv/
venv/
ENV/
.envrc

# VSCode
.vscode/

# OS
.DS_Store
Thumbs.db

# Jupyter
.ipynb_checkpoints/

# Test/coverage
htmlcov/
.coverage
.tox/
.nox/
.cache/
pytest_cache/

# mypy
.mypy_cache/
.dmypy.json

# PyInstaller
*.spec

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pyright
pyrightconfig.json

# Local settings
settings.json

# Backup files
*~
*.bak
*.swp
*.swo

# Others
*.orig

# Ignore secrets
secrets.json
