from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from database import get_db
from models import Device, SensorReading, Emergency, User
from schemas import DevicePairRequest, SensorData
from auth import get_current_user, require_verified_user
from utils import calculate_urgency, find_nearest_station_with_vehicles
import uuid

router = APIRouter()

@router.post("/pair")
def pair_device(pair_request: DevicePairRequest, db: Session = Depends(get_db)):
    # Check if device is already paired
    existing_device = db.query(Device).filter(Device.mac_address == pair_request.mac_address).first()
    if (existing_device):
        raise HTTPException(status_code=400, detail="Device already paired")
    
    device_id = str(uuid.uuid4())
    device = Device(
        id=device_id,
        mac_address=pair_request.mac_address,
        device_name=pair_request.device_name,
        user_id=pair_request.user_id,
    )
    db.add(device)
    db.commit()
    return {"message": "Device paired successfully"}

@router.post("/data")
def receive_sensor_data(sensor_data: SensorData, db: Session = Depends(get_db)):
    # Find device by MAC address
    device = db.query(Device).filter(Device.mac_address == sensor_data.mac_address).first()
    if not device:
        raise HTTPException(status_code=404, detail="Device not found or not paired")
    
    # Store sensor reading
    reading = SensorReading(
        device_id=device.id,
        mac_address=sensor_data.mac_address,
        latitude=sensor_data.latitude,
        longitude=sensor_data.longitude,
        smoke_level=sensor_data.smoke_level
    )
    db.add(reading)
    
    # Check if emergency threshold is exceeded
    if sensor_data.smoke_level > 2000:
        urgency = calculate_urgency(sensor_data.smoke_level)
        
        # Find nearest station with available vehicles
        station = find_nearest_station_with_vehicles(sensor_data.latitude, sensor_data.longitude, db)
        station_id = station.id if station else None
        
        # Create emergency record
        emergency = Emergency(
            device_id=device.id,
            latitude=sensor_data.latitude,
            longitude=sensor_data.longitude,
            smoke_level=sensor_data.smoke_level,
            urgency_level=urgency,
            station_id=station_id
        )
        db.add(emergency)
    
    db.commit()
    return {"message": "Sensor data received successfully"}

@router.get("/registered/")
def get_registered_device(user_id: str, db: Session = Depends(get_db)):
    """Return the registered device for the user with the given user_id."""
    device = db.query(Device).filter(Device.user_id == user_id).first()
    if not device:
        return {}
    return {
        "id": device.id,
        "device_type": getattr(device, 'device_name', None),
        "mac_address": device.mac_address,
        "is_active": getattr(device, 'is_active', True),
    }

@router.post("/trigger-alert")
def trigger_alert(user_id: str, db: Session = Depends(get_db)):
    """Trigger an alert by simulating a device reading with high smoke level for the user's device."""
    # Find the user's device
    device = db.query(Device).filter(Device.user_id == user_id).first()
    if not device:
        raise HTTPException(status_code=404, detail="No device found for user")
    # Get the latest reading for this device
    reading = db.query(SensorReading).filter(SensorReading.device_id == device.id).order_by(SensorReading.timestamp.desc()).first()
    if not reading:
        raise HTTPException(status_code=404, detail="No readings found for device")
    # Simulate a new reading with high smoke level
    new_reading = SensorReading(
        device_id=device.id,
        mac_address=device.mac_address,
        latitude=reading.latitude,
        longitude=reading.longitude,
        smoke_level=3000
    )
    db.add(new_reading)
    # Check if emergency threshold is exceeded (should always be true)
    urgency = calculate_urgency(3000)
    # Find nearest station with available vehicles
    station = find_nearest_station_with_vehicles(reading.latitude, reading.longitude, db)
    station_id = station.id if station else None
    emergency = Emergency(
        device_id=device.id,
        latitude=reading.latitude,
        longitude=reading.longitude,
        smoke_level=3000,
        urgency_level=urgency,
        station_id=station_id
    )
    db.add(emergency)
    db.commit()
    return {"message": "Alert triggered successfully"}