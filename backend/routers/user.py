from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List
from database import get_db
from models import User, SensorReading, Device, Emergency
from schemas import SensorReadingResponse, EmergencyResponse
from auth import require_verified_user

router = APIRouter()

@router.get("/readings", response_model=List[SensorReadingResponse])
def get_user_readings(current_user: User = Depends(require_verified_user), db: Session = Depends(get_db)):
    # Get readings from user's devices
    readings = db.query(SensorReading).join(Device).filter(
        Device.user_id == current_user.id
    ).order_by(SensorReading.timestamp.desc()).limit(100).all()
    
    return [SensorReadingResponse.from_orm(reading) for reading in readings]

@router.get("/emergencies", response_model=List[EmergencyResponse])
def get_user_emergencies(current_user: User = Depends(require_verified_user), db: Session = Depends(get_db)):
    # Get emergencies from user's devices
    emergencies = db.query(Emergency).join(Device).filter(
        Device.user_id == current_user.id
    ).order_by(Emergency.created_at.desc()).all()
    
    return [EmergencyResponse.from_orm(emergency) for emergency in emergencies]

@router.get("/devices")
def get_user_devices(current_user: User = Depends(require_verified_user), db: Session = Depends(get_db)):
    devices = db.query(Device).filter(Device.user_id == current_user.id).all()
    return [
        {
            "id": device.id,
            "mac_address": device.mac_address,
            "device_name": device.device_name,
            "is_active": device.is_active,
            "paired_at": device.paired_at
        }
        for device in devices
    ]