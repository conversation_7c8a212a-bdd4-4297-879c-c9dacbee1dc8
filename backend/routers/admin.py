from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List
from database import get_db
from models import User, FireStation, UserRole
from schemas import UserCreate, UserResponse, FireStationCreate, FireStationResponse, TeamReassignmentRequest
from auth import require_admin, hash_password
import uuid
from utils import generate_random_password, send_email_brevo

router = APIRouter()

@router.post("/users", response_model=UserResponse)
def create_user(user: UserCreate, db: Session = Depends(get_db)):#, admin: User = Depends(require_admin)):
    # Check if user already exists
    if db.query(User).filter(User.email == user.email).first():
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Generate random password
    random_password = generate_random_password()
    print(f"[EMAIL DEBUG] Generated password for {user.email}: {random_password}")
    hashed_password = hash_password(random_password)
    user_id = str(uuid.uuid4())
    
    db_user = User(
        id=user_id,
        email=user.email,
        full_name=user.full_name,
        hashed_password=hashed_password,
        role=user.role,
        phone_number=user.phone_number,
        station_id=user.station_id,
        is_verified=True,  # Admin-created users are auto-verified
        has_changed_default_password=False  # New users must change password
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    # Send email with password
    print(f"[EMAIL DEBUG] Sending account email to {user.email} ...")
    send_email_brevo(
        to_email=user.email,
        to_name=user.full_name,
        subject="Your MyGuardian+ Account Password",
        html_content=f"<p>Hello {user.full_name},</p><p>Your account has been created. Your password is: <b>{random_password}</b></p>"
    )
    print(f"[EMAIL DEBUG] Email sent to {user.email}")
    
    return UserResponse.from_orm(db_user)

@router.get("/users", response_model=List[UserResponse])
def list_users(db: Session = Depends(get_db)):#, admin: User = Depends(require_admin)):
    users = db.query(User).all()
    return [UserResponse.from_orm(user) for user in users]

@router.post("/stations", response_model=FireStationResponse)
def create_fire_station(station: FireStationCreate, db: Session = Depends(get_db)): #, admin: User = Depends(require_admin)):
    db_station = FireStation(
        name=station.name,
        latitude=station.latitude,
        longitude=station.longitude,
        region=station.region,
        district=station.district,
        address=station.address,
        phone_number=station.phone_number
    )
    db.add(db_station)
    db.commit()
    db.refresh(db_station)
    
    # Create station manager if provided
    if station.manager_email and station.manager_name:
        if db.query(User).filter(User.email == station.manager_email).first():
            raise HTTPException(status_code=400, detail="Manager email already registered")
        
        random_password = generate_random_password()
        print(f"[EMAIL DEBUG] Generated password for {station.manager_email}: {random_password}")
        hashed_password = hash_password(random_password)
        manager_id = str(uuid.uuid4())
        
        manager = User(
            id=manager_id,
            email=station.manager_email,
            full_name=station.manager_name,
            hashed_password=hashed_password,
            role=UserRole.STATION_MANAGER,
            phone_number=station.manager_phone or "",
            station_id=db_station.id,
            is_verified=True
        )
        db.add(manager)
        db.commit()
        
        # Send email to manager
        print(f"[EMAIL DEBUG] Sending station manager email to {station.manager_email} ...")
        send_email_brevo(
            to_email=station.manager_email,
            to_name=station.manager_name,
            subject="Your MyGuardian+ Station Manager Account",
            html_content=f"<p>Hello {station.manager_name},</p><p>Your station manager account has been created. Your password is: <b>{random_password}</b></p>"
        )
        print(f"[EMAIL DEBUG] Email sent to {station.manager_email}")
    
    return FireStationResponse.from_orm(db_station)

@router.get("/stations", response_model=List[FireStationResponse])
def list_fire_stations(db: Session = Depends(get_db)): #, admin: User = Depends(require_admin)):
    stations = db.query(FireStation).all()
    return [FireStationResponse.from_orm(station) for station in stations]

@router.get("/stations/{station_id}", response_model=FireStationResponse)
def get_fire_station(station_id: int, db: Session = Depends(get_db)):
    station = db.query(FireStation).filter(FireStation.id == station_id).first()
    if not station:
        raise HTTPException(status_code=404, detail="Station not found")
    return FireStationResponse.from_orm(station)

@router.post("/reassign-responder")
def reassign_responder(
    request: TeamReassignmentRequest,
    db: Session = Depends(get_db),
    admin: User = Depends(require_admin)
):
    """Reassign a responder to a different station"""

    # Get the user to reassign
    user = db.query(User).filter(User.id == request.user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Check if user is a responder
    if user.role != UserRole.RESPONDER:
        raise HTTPException(status_code=400, detail="Only responders can be reassigned")

    # Check if the new station exists
    new_station = db.query(FireStation).filter(FireStation.id == request.new_station_id).first()
    if not new_station:
        raise HTTPException(status_code=404, detail="Station not found")

    # Update the user's station
    old_station_id = user.station_id
    user.station_id = request.new_station_id

    # Clear vehicle assignment when changing stations
    user.vehicle_id = None

    db.commit()
    db.refresh(user)

    return {
        "message": f"User {user.full_name} successfully reassigned from station {old_station_id} to {new_station.name}",
        "user_id": user.id,
        "old_station_id": old_station_id,
        "new_station_id": request.new_station_id,
        "reason": request.reason
    }