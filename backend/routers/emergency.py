from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from database import get_db
from models import Emergency, EmergencyStatus, User, UserRole
from schemas import FireResponseReport
from auth import get_current_user
from datetime import datetime

router = APIRouter()

@router.post("/resolve")
def resolve_emergency_with_report(
    report: FireResponseReport,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Resolve an emergency with a fire response report"""
    
    # Check if user is a responder
    if current_user.role != UserRole.RESPONDER:
        raise HTTPException(status_code=403, detail="Only responders can resolve emergencies")
    
    # Get the emergency
    emergency = db.query(Emergency).filter(Emergency.id == report.emergency_id).first()
    if not emergency:
        raise HTTPException(status_code=404, detail="Emergency not found")
    
    # Check if emergency is already resolved
    if emergency.status == EmergencyStatus.RESOLVED:
        raise HTTPException(status_code=400, detail="Emergency is already resolved")
    
    # Check if the responder is assigned to the same station as the emergency
    if current_user.station_id != emergency.station_id:
        raise HTTPException(status_code=403, detail="You can only resolve emergencies from your station")
    
    # Update emergency status and add report data
    emergency.status = EmergencyStatus.RESOLVED
    emergency.resolved_at = datetime.utcnow()
    
    # Store report data in emergency record (you might want a separate reports table in production)
    emergency.cause_determined = report.cause_determined
    emergency.cause_description = report.cause_description
    emergency.water_used_liters = report.water_used_liters
    emergency.fuel_used_liters = report.fuel_used_liters
    emergency.personnel_count = report.personnel_count
    emergency.response_duration_minutes = report.response_duration_minutes
    emergency.property_damage_estimate = report.property_damage_estimate
    emergency.injuries_count = report.injuries_count
    emergency.fatalities_count = report.fatalities_count
    emergency.additional_notes = report.additional_notes
    emergency.resolved_by_user_id = current_user.id
    
    db.commit()
    db.refresh(emergency)
    
    return {
        "message": "Emergency resolved successfully",
        "emergency_id": emergency.id,
        "resolved_at": emergency.resolved_at,
        "resolved_by": current_user.full_name
    }

@router.get("/{emergency_id}/report")
def get_emergency_report(
    emergency_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get the fire response report for an emergency"""
    
    emergency = db.query(Emergency).filter(Emergency.id == emergency_id).first()
    if not emergency:
        raise HTTPException(status_code=404, detail="Emergency not found")
    
    # Check permissions - admin can see all, others only their station's emergencies
    if current_user.role != UserRole.ADMIN and current_user.station_id != emergency.station_id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    if emergency.status != EmergencyStatus.RESOLVED:
        raise HTTPException(status_code=400, detail="Emergency is not resolved yet")
    
    return {
        "emergency_id": emergency.id,
        "cause_determined": emergency.cause_determined,
        "cause_description": emergency.cause_description,
        "water_used_liters": emergency.water_used_liters,
        "fuel_used_liters": emergency.fuel_used_liters,
        "personnel_count": emergency.personnel_count,
        "response_duration_minutes": emergency.response_duration_minutes,
        "property_damage_estimate": emergency.property_damage_estimate,
        "injuries_count": emergency.injuries_count,
        "fatalities_count": emergency.fatalities_count,
        "additional_notes": emergency.additional_notes,
        "resolved_at": emergency.resolved_at,
        "resolved_by_user_id": emergency.resolved_by_user_id
    }
