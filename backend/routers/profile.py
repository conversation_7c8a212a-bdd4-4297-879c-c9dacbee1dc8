from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from database import get_db
from models import User, FireStation
from schemas import PasswordChangeRequest, UserProfileUpdate, UserResponse
from auth import get_current_user, verify_password, hash_password

router = APIRouter()

@router.get("/me", response_model=UserResponse)
def get_current_user_profile(current_user: User = Depends(get_current_user)):
    """Get current user's profile information"""
    return UserResponse.from_orm(current_user)

@router.put("/me", response_model=UserResponse)
def update_current_user_profile(
    profile_update: UserProfileUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update current user's profile information"""
    
    # Update only provided fields
    if profile_update.full_name is not None:
        current_user.full_name = profile_update.full_name
    if profile_update.phone_number is not None:
        current_user.phone_number = profile_update.phone_number
    if profile_update.emergency_contact_name is not None:
        current_user.emergency_contact_name = profile_update.emergency_contact_name
    if profile_update.emergency_contact_phone is not None:
        current_user.emergency_contact_phone = profile_update.emergency_contact_phone
    if profile_update.medical_conditions is not None:
        current_user.medical_conditions = profile_update.medical_conditions
    if profile_update.medications is not None:
        current_user.medications = profile_update.medications
    
    db.commit()
    db.refresh(current_user)
    return UserResponse.from_orm(current_user)

@router.post("/change-password")
def change_password(
    password_change: PasswordChangeRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Change user's password"""
    
    # Verify current password
    if not verify_password(password_change.current_password, current_user.hashed_password):
        raise HTTPException(status_code=400, detail="Current password is incorrect")
    
    # Hash new password
    new_hashed_password = hash_password(password_change.new_password)
    
    # Update password and mark as changed from default
    current_user.hashed_password = new_hashed_password
    current_user.has_changed_default_password = True
    
    db.commit()
    
    return {"message": "Password changed successfully"}

@router.get("/station-info")
def get_user_station_info(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's station information"""
    
    if not current_user.station_id:
        return {"station": None}
    
    station = db.query(FireStation).filter(FireStation.id == current_user.station_id).first()
    if not station:
        return {"station": None}
    
    return {
        "station": {
            "id": station.id,
            "name": station.name,
            "district": station.district,
            "region": station.region,
            "address": station.address,
            "phone_number": station.phone_number
        }
    }

@router.get("/needs-password-change")
def check_password_change_required(current_user: User = Depends(get_current_user)):
    """Check if user needs to change their default password"""
    return {
        "needs_password_change": not current_user.has_changed_default_password,
        "user_id": current_user.id
    }
