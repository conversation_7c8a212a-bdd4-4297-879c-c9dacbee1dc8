from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Dict, Any
from database import get_db
from models import User, UserR<PERSON>, Device, SensorReading, Emergency, EmergencyContact
from schemas import MobileUserRegister, UserResponse, Token, LoginRequest
from auth import hash_password, create_access_token, verify_password, get_current_user
from datetime import datetime
import uuid

router = APIRouter()

@router.post("/register")
def register_mobile_user(user_data: MobileUserRegister, db: Session = Depends(get_db)):
    """Register a new end user via mobile app - simplified without phone verification"""
    
    # Check if email already exists
    if db.query(User).filter(User.email == user_data.email).first():
        raise HTTPException(status_code=400, detail="Email already registered")
    
    # Create user (verified by default for mobile registration)
    hashed_password = hash_password(user_data.password)
    user_id = str(uuid.uuid4())
    
    db_user = User(
        id=user_id,
        email=user_data.email,
        full_name=user_data.full_name,
        hashed_password=hashed_password,
        role=UserRole.USER,
        phone_number=user_data.phone_number,
        is_verified=True,  # Auto-verified for mobile users
        is_active=True
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    # Return user data similar to your mobile app format
    return {
        "id": db_user.id,
        "email": db_user.email,
        "phone_number": db_user.phone_number,
        "department": None,
        "role": db_user.role,
        "username": db_user.email.split('@')[0],
        "first_name": user_data.full_name.split(' ')[0] if ' ' in user_data.full_name else user_data.full_name,
        "last_name": user_data.full_name.split(' ')[-1] if ' ' in user_data.full_name else 'Last',
        "full_name": db_user.full_name,
        "is_superuser": False,
        "is_staff": False,
        "is_active": True,
        "date_joined": db_user.created_at,
        "badge_number": None,
        "rank": "User",
        "years_of_service": 0,
        "certifications": "None",
        "emergency_contact_name": "N/A",
        "emergency_contact_phone": "**********",
        "medical_conditions": "None",
        "medications": "None",
        "allergies": "None",
        "blood_type": "O+",
        "is_active_user": True,
        "created_at": db_user.created_at,
        "updated_at": db_user.created_at,
        "employee_id": None
    }

@router.post("/login")
def mobile_login(login_data: LoginRequest, db: Session = Depends(get_db)):
    """Login for mobile users - returns full user data"""
    
    user = db.query(User).filter(User.email == login_data.email).first()
    if not user or not verify_password(login_data.password, user.hashed_password):
        raise HTTPException(status_code=401, detail="Incorrect email or password")
    
    if not user.is_active:
        raise HTTPException(status_code=401, detail="Account is deactivated")
    
    # Return full user data like your mobile app expects
    return {
        "id": user.id,
        "email": user.email,
        "phone_number": user.phone_number,
        "department": getattr(user, 'department', None),
        "role": user.role,
        "username": user.email.split('@')[0],
        "first_name": user.full_name.split(' ')[0] if ' ' in user.full_name else user.full_name,
        "last_name": user.full_name.split(' ')[-1] if ' ' in user.full_name else 'Last',
        "full_name": user.full_name,
        "is_superuser": False,
        "is_staff": False,
        "is_active": True,
        "date_joined": user.created_at,
        "badge_number": getattr(user, 'badge_number', None),
        "rank": getattr(user, 'rank', 'User'),
        "years_of_service": getattr(user, 'years_of_service', 0),
        "certifications": getattr(user, 'certifications', 'None'),
        "emergency_contact_name": getattr(user, 'emergency_contact_name', 'N/A'),
        "emergency_contact_phone": getattr(user, 'emergency_contact_phone', '**********'),
        "medical_conditions": getattr(user, 'medical_conditions', 'None'),
        "medications": getattr(user, 'medications', 'None'),
        "allergies": getattr(user, 'allergies', 'None'),
        "blood_type": getattr(user, 'blood_type', 'O+'),
        "is_active_user": True,
        "created_at": user.created_at,
        "updated_at": user.created_at,
        "employee_id": getattr(user, 'employee_id', None)
    }

@router.get("/device-id")
def get_device_id(user_id: str, db: Session = Depends(get_db)):
    """Fetch device ID for user - equivalent to your device_reading_service query"""
    
    device = db.query(Device).filter(Device.user_id == user_id).first()
    if not device:
        return {}  # Empty result like your mobile app expects
    
    return {"device_id": device.id}

@router.get("/device-readings")
def get_device_readings(device_id: str, db: Session = Depends(get_db)):
    """Fetch device readings - equivalent to your device_reading_service query"""
    
    readings = db.query(SensorReading).filter(
        SensorReading.device_id == device_id
    ).order_by(SensorReading.timestamp.desc()).limit(50).all()
    
    # Return all columns like your mobile app expects
    return [
        {
            "id": reading.id,
            "device_id": reading.device_id,
            "mac_address": reading.mac_address,
            "latitude": reading.latitude,
            "longitude": reading.longitude,
            "smoke_level": reading.smoke_level,
            "timestamp": reading.timestamp,
        }
        for reading in readings
    ]

@router.get("/alert-history/device/{device_id}")
def get_device_alert_history(device_id: str, db: Session = Depends(get_db)):
    """Device-related alerts - equivalent to your emergency_triggers query"""
    
    emergencies = db.query(Emergency).filter(
        Emergency.device_id == device_id
    ).order_by(Emergency.created_at.desc()).limit(50).all()
    
    # Map to your mobile app's emergency_triggers format
    return [
        {
            "trigger_id": emergency.id,
            "trigger_type": "smoke_detection",
            "severity": emergency.urgency_level,
            "trigger_value": emergency.smoke_level,
            "threshold_value": 2000,
            "latitude": emergency.latitude,
            "longitude": emergency.longitude,
            "triggered_at": emergency.created_at,
            "acknowledged": emergency.status != "active",
            "acknowledged_at": emergency.resolved_at if emergency.status != "active" else None,
            "resolved_at": emergency.resolved_at,
            "device_id": emergency.device_id,
            "reading_id": None,
            "alert_created_id": emergency.id
        }
        for emergency in emergencies
    ]

@router.get("/alert-history/user/{user_id}")
def get_user_alert_history(user_id: str, db: Session = Depends(get_db)):
    """User-created alerts - placeholder for your alerts_alert table structure"""
    
    # This would map to your alerts_alert table when implemented
    # For now, return emergency data related to user's devices
    emergencies = db.query(Emergency).join(Device).filter(
        Device.user_id == user_id
    ).order_by(Emergency.created_at.desc()).limit(50).all()
    
    return [
        {
            "id": emergency.id,
            "title": f"Fire Alert - Smoke Level {emergency.smoke_level}",
            "alert_type": "fire_detection",
            "description": f"High smoke level detected: {emergency.smoke_level}",
            "location": f"{emergency.latitude}, {emergency.longitude}",
            "priority": emergency.urgency_level,
            "status": emergency.status,
            "department": None,
            "assigned_to": None,
            "created_at": emergency.created_at,
            "updated_at": emergency.created_at,
            "resolved_at": emergency.resolved_at,
            "response_time": None,
            "outcome": "Resolved" if emergency.resolved_at else "Pending",
            "full_name": None
        }
        for emergency in emergencies
    ]

@router.post("/emergency-contacts")
def create_emergency_contact(contact_data: dict, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Create emergency contact"""
    
    contact_id = str(uuid.uuid4())
    now = datetime.utcnow()
    
    contact = EmergencyContact(
        contact_id=contact_id,
        user_id=current_user.id,
        name=contact_data.get('name'),
        phone_number=contact_data.get('phone_number'),
        relation=contact_data.get('relation'),
        preferred_method=contact_data.get('preferred_method', 'phone'),
        created_at=now,
        updated_at=now
    )
    db.add(contact)
    db.commit()
    db.refresh(contact)
    
    return {
        "contact_id": contact.contact_id,
        "user_id": contact.user_id,
        "name": contact.name,
        "phone_number": contact.phone_number,
        "relation": contact.relation,
        "preferred_method": contact.preferred_method,
        "created_at": contact.created_at,
        "updated_at": contact.updated_at
    }

@router.get("/emergency-contacts")
def get_emergency_contacts(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Get user's emergency contacts"""
    
    contacts = db.query(EmergencyContact).filter(
        EmergencyContact.user_id == current_user.id
    ).order_by(EmergencyContact.created_at.desc()).all()
    
    return [
        {
            "contact_id": contact.contact_id,
            "user_id": contact.user_id,
            "name": contact.name,
            "phone_number": contact.phone_number,
            "relation": contact.relation,
            "preferred_method": contact.preferred_method,
            "created_at": contact.created_at,
            "updated_at": contact.updated_at
        }
        for contact in contacts
    ]

@router.put("/emergency-contacts/{contact_id}")
def update_emergency_contact(contact_id: str, updates: dict, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Update emergency contact"""
    
    contact = db.query(EmergencyContact).filter(
        EmergencyContact.contact_id == contact_id,
        EmergencyContact.user_id == current_user.id
    ).first()
    
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")
    
    # Update fields
    for field, value in updates.items():
        if hasattr(contact, field):
            setattr(contact, field, value)
    
    contact.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(contact)
    
    return {
        "contact_id": contact.contact_id,
        "user_id": contact.user_id,
        "name": contact.name,
        "phone_number": contact.phone_number,
        "relation": contact.relation,
        "preferred_method": contact.preferred_method,
        "created_at": contact.created_at,
        "updated_at": contact.updated_at
    }

@router.delete("/emergency-contacts/{contact_id}")
def delete_emergency_contact(contact_id: str, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Delete emergency contact"""
    
    contact = db.query(EmergencyContact).filter(
        EmergencyContact.contact_id == contact_id,
        EmergencyContact.user_id == current_user.id
    ).first()
    
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")
    
    contact_data = {
        "contact_id": contact.contact_id,
        "user_id": contact.user_id,
        "name": contact.name,
        "phone_number": contact.phone_number,
        "relation": contact.relation,
        "preferred_method": contact.preferred_method,
        "created_at": contact.created_at,
        "updated_at": contact.updated_at
    }
    
    db.delete(contact)
    db.commit()
    
    return contact_data

@router.post("/devices/register")
def register_device(device_data: dict, db: Session = Depends(get_db)):
    """Device registration - equivalent to your Bluetooth scan widget POST"""
    
    # Check if device already exists
    existing_device = db.query(Device).filter(
        Device.mac_address == device_data.get('mac_address')
    ).first()
    
    if existing_device:
        raise HTTPException(status_code=400, detail="Device already registered")
    
    device_id = str(uuid.uuid4())
    serial_number = f"SN-{device_id[:8].upper()}"
    
    # Create device
    device = Device(
        id=device_id,
        mac_address=device_data.get('mac_address'),
        device_name=device_data.get('name', 'MyGuardian+ Device'),
        user_id=device_data.get('owner_id'),
        is_active=device_data.get('is_active', True)
    )
    
    db.add(device)
    db.commit()
    
    return {
        "message": "Device registered successfully",
        "device_id": device_id,
        "serial_number": serial_number
    }