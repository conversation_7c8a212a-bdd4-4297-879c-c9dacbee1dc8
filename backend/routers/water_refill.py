from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List
from database import get_db
from models import WaterRefillPoint
from schemas import WaterRefillPointCreate, WaterRefillPointResponse
from auth import require_station_manager, require_admin

router = APIRouter(prefix="/water-refill", tags=["Water Refill Points"])

@router.get("/", response_model=List[WaterRefillPointResponse])
def list_refill_points(db: Session = Depends(get_db)):
    return db.query(WaterRefillPoint).all()

@router.post("/", response_model=WaterRefillPointResponse)
def add_refill_point(
    refill: WaterRefillPointCreate,
    db: Session = Depends(get_db),
    manager=Depends(require_admin),
):
    point = WaterRefillPoint(**refill.dict())
    db.add(point)
    db.commit()
    db.refresh(point)
    return point
