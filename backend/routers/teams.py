from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from sqlalchemy import and_
from database import get_db
from models import User, Team, TeamMember, DutyShift, Vehicle, VehicleDriver, UserRole
from schemas import (
    TeamCreate, TeamResponse, TeamMemberAdd, TeamMemberResponse,
    DutyShiftCreate, DutyShiftResponse, VehicleDriverAssign, VehicleDriverResponse,
    ClockInRequest, UserResponse
)
from auth import get_current_user
from datetime import datetime
from typing import List

router = APIRouter()

@router.post("/teams", response_model=TeamResponse)
def create_team(
    team_data: TeamCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new team (Station Manager only)"""
    
    if current_user.role != UserRole.STATION_MANAGER:
        raise HTTPException(status_code=403, detail="Only station managers can create teams")
    
    if current_user.station_id != team_data.station_id:
        raise HTTPException(status_code=403, detail="Can only create teams for your own station")
    
    team = Team(
        name=team_data.name,
        station_id=team_data.station_id,
        created_by=current_user.id
    )
    
    db.add(team)
    db.commit()
    db.refresh(team)
    
    return team

@router.get("/teams", response_model=List[TeamResponse])
def get_teams(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get teams for current user's station"""
    
    if not current_user.station_id:
        return []
    
    teams = db.query(Team).filter(
        and_(
            Team.station_id == current_user.station_id,
            Team.is_active == True
        )
    ).all()
    
    return teams

@router.post("/teams/{team_id}/members", response_model=TeamMemberResponse)
def add_team_member(
    team_id: int,
    member_data: TeamMemberAdd,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add a member to a team (Station Manager only)"""
    
    if current_user.role != UserRole.STATION_MANAGER:
        raise HTTPException(status_code=403, detail="Only station managers can manage team members")
    
    # Check if team exists and belongs to current user's station
    team = db.query(Team).filter(Team.id == team_id).first()
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")
    
    if team.station_id != current_user.station_id:
        raise HTTPException(status_code=403, detail="Can only manage teams in your station")
    
    # Check if user exists and is a responder in the same station
    user = db.query(User).filter(User.id == member_data.user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    if user.role != UserRole.RESPONDER or user.station_id != current_user.station_id:
        raise HTTPException(status_code=400, detail="Can only add responders from your station to teams")
    
    # Check if user is already in the team
    existing_member = db.query(TeamMember).filter(
        and_(
            TeamMember.team_id == team_id,
            TeamMember.user_id == member_data.user_id,
            TeamMember.is_active == True
        )
    ).first()
    
    if existing_member:
        raise HTTPException(status_code=400, detail="User is already a member of this team")
    
    team_member = TeamMember(
        team_id=team_id,
        user_id=member_data.user_id,
        role=member_data.role
    )
    
    db.add(team_member)
    db.commit()
    db.refresh(team_member)
    
    return team_member

@router.get("/teams/{team_id}/members", response_model=List[TeamMemberResponse])
def get_team_members(
    team_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get members of a team"""
    
    # Check if team exists and user has access
    team = db.query(Team).filter(Team.id == team_id).first()
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")
    
    if team.station_id != current_user.station_id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    members = db.query(TeamMember).filter(
        and_(
            TeamMember.team_id == team_id,
            TeamMember.is_active == True
        )
    ).all()
    
    return members

@router.post("/teams/{team_id}/add-member", response_model=TeamMemberResponse)
def add_team_member_alt(
    team_id: int,
    member_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Alternative endpoint for adding team members"""
    # Get the team
    team = db.query(Team).filter(Team.id == team_id).first()
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")

    if team.station_id != current_user.station_id:
        raise HTTPException(status_code=403, detail="Can only manage teams in your station")

    # Check if user exists and is a responder in the same station
    user = db.query(User).filter(User.id == member_data["user_id"]).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    if user.role != UserRole.RESPONDER or user.station_id != current_user.station_id:
        raise HTTPException(status_code=400, detail="Can only add responders from your station to teams")

    # Check if user is already in the team
    existing_member = db.query(TeamMember).filter(
        TeamMember.team_id == team_id,
        TeamMember.user_id == member_data["user_id"]
    ).first()

    if existing_member:
        raise HTTPException(status_code=400, detail="User is already a member of this team")

    # Create team member
    team_member = TeamMember(
        team_id=team_id,
        user_id=member_data["user_id"],
        role=member_data.get("role", "member"),
        joined_at=datetime.utcnow()
    )

    db.add(team_member)
    db.commit()
    db.refresh(team_member)

    return team_member

@router.delete("/teams/{team_id}/remove-member")
def remove_team_member(
    team_id: int,
    member_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Remove a member from a team"""
    # Get the team
    team = db.query(Team).filter(Team.id == team_id).first()
    if not team:
        raise HTTPException(status_code=404, detail="Team not found")

    if team.station_id != current_user.station_id:
        raise HTTPException(status_code=403, detail="Can only manage teams in your station")

    # Find the team member
    team_member = db.query(TeamMember).filter(
        TeamMember.team_id == team_id,
        TeamMember.user_id == member_data["user_id"]
    ).first()

    if not team_member:
        raise HTTPException(status_code=404, detail="User is not a member of this team")

    # Remove the member
    db.delete(team_member)
    db.commit()

    return {"message": "Member removed successfully"}

@router.post("/clock-in", response_model=List[DutyShiftResponse])
def clock_in_users(
    clock_in_data: ClockInRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Clock in users for duty (Station Manager or self clock-in)"""
    
    duty_shifts = []
    
    for user_id in clock_in_data.user_ids:
        # Check permissions
        if current_user.role == UserRole.STATION_MANAGER:
            # Station manager can clock in anyone in their station
            user = db.query(User).filter(User.id == user_id).first()
            if not user or user.station_id != current_user.station_id:
                continue
        elif current_user.id == user_id:
            # Users can clock themselves in
            user = current_user
        else:
            # Can't clock in other users unless you're a station manager
            continue
        
        # Check if user is already clocked in
        existing_shift = db.query(DutyShift).filter(
            and_(
                DutyShift.user_id == user_id,
                DutyShift.status == "on_duty",
                DutyShift.shift_end.is_(None)
            )
        ).first()
        
        if existing_shift:
            continue  # Already clocked in
        
        duty_shift = DutyShift(
            user_id=user_id,
            team_id=clock_in_data.team_id,
            vehicle_id=clock_in_data.vehicle_id,
            clocked_in_by=current_user.id
        )
        
        db.add(duty_shift)
        duty_shifts.append(duty_shift)
    
    db.commit()
    
    for shift in duty_shifts:
        db.refresh(shift)
    
    return duty_shifts

@router.post("/clock-out/{user_id}")
def clock_out_user(
    user_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Clock out a user from duty"""

    # Check permissions
    if current_user.role != UserRole.STATION_MANAGER and current_user.id != user_id:
        raise HTTPException(status_code=403, detail="Can only clock out yourself or be a station manager")
    
    # Find active duty shift
    duty_shift = db.query(DutyShift).filter(
        and_(
            DutyShift.user_id == user_id,
            DutyShift.status == "on_duty",
            DutyShift.shift_end.is_(None)
        )
    ).first()
    
    if not duty_shift:
        raise HTTPException(status_code=404, detail="No active duty shift found")
    
    duty_shift.shift_end = datetime.utcnow()
    duty_shift.status = "off_duty"
    
    db.commit()
    
    return {"message": "Successfully clocked out"}

@router.get("/duty-status", response_model=List[DutyShiftResponse])
def get_duty_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current duty status for station"""
    
    if not current_user.station_id:
        return []
    
    # Get all active duty shifts for the station
    active_shifts = db.query(DutyShift).join(User, DutyShift.user_id == User.id).filter(
        and_(
            User.station_id == current_user.station_id,
            DutyShift.status == "on_duty",
            DutyShift.shift_end.is_(None)
        )
    ).all()
    
    return active_shifts

@router.post("/vehicles/{vehicle_id}/drivers", response_model=VehicleDriverResponse)
def assign_driver_to_vehicle(
    vehicle_id: int,
    driver_data: VehicleDriverAssign,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Assign a driver to a vehicle (Station Manager only)"""
    
    if current_user.role != UserRole.STATION_MANAGER:
        raise HTTPException(status_code=403, detail="Only station managers can assign drivers")
    
    # Check if vehicle exists and belongs to current user's station
    vehicle = db.query(Vehicle).filter(Vehicle.id == vehicle_id).first()
    if not vehicle:
        raise HTTPException(status_code=404, detail="Vehicle not found")
    
    if vehicle.station_id != current_user.station_id:
        raise HTTPException(status_code=403, detail="Can only manage vehicles in your station")
    
    # Check if driver exists and is a driver in the same station
    driver = db.query(User).filter(User.id == driver_data.driver_id).first()
    if not driver:
        raise HTTPException(status_code=404, detail="Driver not found")
    
    if driver.role != UserRole.DRIVER or driver.station_id != current_user.station_id:
        raise HTTPException(status_code=400, detail="Can only assign drivers from your station")
    
    # Check if driver is already assigned to this vehicle
    existing_assignment = db.query(VehicleDriver).filter(
        and_(
            VehicleDriver.vehicle_id == vehicle_id,
            VehicleDriver.driver_id == driver_data.driver_id,
            VehicleDriver.is_active == True
        )
    ).first()
    
    if existing_assignment:
        raise HTTPException(status_code=400, detail="Driver is already assigned to this vehicle")
    
    vehicle_driver = VehicleDriver(
        vehicle_id=vehicle_id,
        driver_id=driver_data.driver_id
    )
    
    db.add(vehicle_driver)
    db.commit()
    db.refresh(vehicle_driver)
    
    return vehicle_driver

@router.get("/vehicles/{vehicle_id}/drivers", response_model=List[VehicleDriverResponse])
def get_vehicle_drivers(
    vehicle_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get drivers assigned to a vehicle"""
    
    # Check if vehicle exists and user has access
    vehicle = db.query(Vehicle).filter(Vehicle.id == vehicle_id).first()
    if not vehicle:
        raise HTTPException(status_code=404, detail="Vehicle not found")
    
    if vehicle.station_id != current_user.station_id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    drivers = db.query(VehicleDriver).filter(
        and_(
            VehicleDriver.vehicle_id == vehicle_id,
            VehicleDriver.is_active == True
        )
    ).all()
    
    return drivers
