"""Add response report fields and password change tracking

Revision ID: add_response_report_fields
Revises: 
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_response_report_fields'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Add has_changed_default_password field to users table
    op.add_column('users', sa.Column('has_changed_default_password', sa.<PERSON>(), nullable=True, default=False))
    
    # Update existing users to have has_changed_default_password = True (assuming they've already changed)
    op.execute("UPDATE users SET has_changed_default_password = TRUE WHERE has_changed_default_password IS NULL")
    
    # Make the column non-nullable after setting default values
    op.alter_column('users', 'has_changed_default_password', nullable=False)
    
    # Add fire response report fields to emergencies table
    op.add_column('emergencies', sa.Column('cause_determined', sa.String(50), nullable=True))
    op.add_column('emergencies', sa.Column('cause_description', sa.Text(), nullable=True))
    op.add_column('emergencies', sa.Column('water_used_liters', sa.Float(), nullable=True))
    op.add_column('emergencies', sa.Column('fuel_used_liters', sa.Float(), nullable=True))
    op.add_column('emergencies', sa.Column('personnel_count', sa.Integer(), nullable=True))
    op.add_column('emergencies', sa.Column('response_duration_minutes', sa.Integer(), nullable=True))
    op.add_column('emergencies', sa.Column('property_damage_estimate', sa.Float(), nullable=True))
    op.add_column('emergencies', sa.Column('injuries_count', sa.Integer(), nullable=True, default=0))
    op.add_column('emergencies', sa.Column('fatalities_count', sa.Integer(), nullable=True, default=0))
    op.add_column('emergencies', sa.Column('additional_notes', sa.Text(), nullable=True))
    op.add_column('emergencies', sa.Column('resolved_by_user_id', sa.Integer(), nullable=True))
    
    # Add foreign key constraint for resolved_by_user_id
    op.create_foreign_key(
        'fk_emergencies_resolved_by_user_id',
        'emergencies', 'users',
        ['resolved_by_user_id'], ['id']
    )


def downgrade():
    # Remove foreign key constraint
    op.drop_constraint('fk_emergencies_resolved_by_user_id', 'emergencies', type_='foreignkey')
    
    # Remove fire response report fields from emergencies table
    op.drop_column('emergencies', 'resolved_by_user_id')
    op.drop_column('emergencies', 'additional_notes')
    op.drop_column('emergencies', 'fatalities_count')
    op.drop_column('emergencies', 'injuries_count')
    op.drop_column('emergencies', 'property_damage_estimate')
    op.drop_column('emergencies', 'response_duration_minutes')
    op.drop_column('emergencies', 'personnel_count')
    op.drop_column('emergencies', 'fuel_used_liters')
    op.drop_column('emergencies', 'water_used_liters')
    op.drop_column('emergencies', 'cause_description')
    op.drop_column('emergencies', 'cause_determined')
    
    # Remove has_changed_default_password field from users table
    op.drop_column('users', 'has_changed_default_password')
