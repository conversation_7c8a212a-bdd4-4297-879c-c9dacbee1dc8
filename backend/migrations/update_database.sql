-- Database migration script to add response report fields and password change tracking
-- Run this script against your database to add the new fields

-- Add has_changed_default_password field to users table
ALTER TABLE users ADD COLUMN has_changed_default_password BOOLEAN DEFAULT FALSE;

-- Update existing users to have has_changed_default_password = TRUE 
-- (assuming they've already changed their passwords)
UPDATE users SET has_changed_default_password = TRUE WHERE has_changed_default_password IS NULL OR has_changed_default_password = FALSE;

-- Add fire response report fields to emergencies table
ALTER TABLE emergencies ADD COLUMN cause_determined VARCHAR(50);
ALTER TABLE emergencies ADD COLUMN cause_description TEXT;
ALTER TABLE emergencies ADD COLUMN water_used_liters FLOAT;
ALTER TABLE emergencies ADD COLUMN fuel_used_liters FLOAT;
ALTER TABLE emergencies ADD COLUMN personnel_count INTEGER;
ALTER TABLE emergencies ADD COLUMN response_duration_minutes INTEGER;
ALTER TABLE emergencies ADD COLUMN property_damage_estimate FLOAT;
ALTER TABLE emergencies ADD COLUMN injuries_count INTEGER DEFAULT 0;
ALTER TABLE emergencies ADD COLUMN fatalities_count INTEGER DEFAULT 0;
ALTER TABLE emergencies ADD COLUMN additional_notes TEXT;
ALTER TABLE emergencies ADD COLUMN resolved_by_user_id INTEGER;

-- Add foreign key constraint for resolved_by_user_id (if your database supports it)
-- For PostgreSQL:
-- ALTER TABLE emergencies ADD CONSTRAINT fk_emergencies_resolved_by_user_id 
--     FOREIGN KEY (resolved_by_user_id) REFERENCES users(id);

-- For MySQL:
-- ALTER TABLE emergencies ADD CONSTRAINT fk_emergencies_resolved_by_user_id 
--     FOREIGN KEY (resolved_by_user_id) REFERENCES users(id);

-- For SQLite (foreign keys need to be enabled):
-- PRAGMA foreign_keys = ON;
-- (Note: SQLite doesn't support adding foreign key constraints to existing tables easily)

-- Verify the changes
SELECT 'Users table updated' as status;
SELECT COUNT(*) as users_with_password_flag FROM users WHERE has_changed_default_password = TRUE;

SELECT 'Emergencies table updated' as status;
DESCRIBE emergencies; -- For MySQL
-- \d emergencies; -- For PostgreSQL
-- .schema emergencies -- For SQLite
