# Database Migration Guide

This directory contains migration scripts to update your database with the new features for the Fire Emergency System.

## What's Being Added

### 1. Password Change Tracking
- Adds `has_changed_default_password` field to the `users` table
- Enables enforcement of password changes for new users
- Existing users are marked as having changed their passwords

### 2. Fire Response Report Fields
Adds the following fields to the `emergencies` table:
- `cause_determined` - Whether the fire cause was determined (yes/no/under_investigation)
- `cause_description` - Detailed description of the determined cause
- `water_used_liters` - Amount of water used in liters
- `fuel_used_liters` - Amount of fuel used in liters
- `personnel_count` - Number of personnel involved in response
- `response_duration_minutes` - Total response time in minutes
- `property_damage_estimate` - Estimated property damage in euros
- `injuries_count` - Number of injuries (default: 0)
- `fatalities_count` - Number of fatalities (default: 0)
- `additional_notes` - Additional observations or notes
- `resolved_by_user_id` - ID of the user who resolved the emergency

## Migration Options

### Option 1: Automated Python Script (Recommended)

Run the automated migration script from the backend directory:

```bash
cd backend
./run_migration.sh
```

This script will:
- Check dependencies
- Activate virtual environment if available
- Run the Python migration script
- Verify the changes
- Provide next steps

### Option 2: Manual Python Script

If you prefer to run the Python script directly:

```bash
cd backend
python3 migrate_database.py
```

### Option 3: Manual SQL Script

If you prefer to run SQL commands manually:

```bash
# For PostgreSQL
psql -d your_database -f migrations/update_database.sql

# For MySQL
mysql -u username -p your_database < migrations/update_database.sql

# For SQLite
sqlite3 your_database.db < migrations/update_database.sql
```

## Rollback

If you need to rollback the migration (remove the new fields):

```bash
cd backend
./run_migration.sh --rollback
```

Or with Python directly:
```bash
python3 migrate_database.py --rollback
```

## Verification

After running the migration, you can verify the changes:

1. **Check Users Table:**
   ```sql
   SELECT COUNT(*) FROM users WHERE has_changed_default_password = TRUE;
   ```

2. **Check Emergencies Table:**
   ```sql
   DESCRIBE emergencies; -- MySQL
   \d emergencies;       -- PostgreSQL
   .schema emergencies   -- SQLite
   ```

## Troubleshooting

### Common Issues

1. **Permission Denied on Shell Script:**
   ```bash
   chmod +x backend/run_migration.sh
   ```

2. **Python Dependencies Missing:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Database Connection Issues:**
   - Check your database is running
   - Verify connection settings in `database.py`
   - Ensure database user has ALTER TABLE permissions

4. **Column Already Exists Errors:**
   - The script checks for existing columns and skips them
   - If you get errors, the migration may have partially completed
   - Check which columns exist and run the script again

### Manual Verification Queries

```sql
-- Check if password tracking field exists
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name = 'has_changed_default_password';

-- Check if report fields exist
SELECT column_name 
FROM information_schema.columns 
WHERE table_name = 'emergencies' 
AND column_name IN ('cause_determined', 'water_used_liters', 'resolved_by_user_id');
```

## After Migration

Once the migration is complete:

1. **Restart your FastAPI server** to load the new model definitions
2. **Test the new features:**
   - User profile management
   - Password change enforcement
   - Enhanced dashboards
   - Team reassignment
   - Fire response reports

## Files in this Directory

- `add_response_report_fields.py` - Alembic migration file
- `update_database.sql` - Raw SQL migration script
- `README.md` - This documentation file

## Support

If you encounter issues with the migration:

1. Check the error messages carefully
2. Verify your database permissions
3. Ensure all dependencies are installed
4. Try running the migration step by step using the SQL script

The migration is designed to be safe and non-destructive - it only adds new fields and doesn't modify existing data.
