"""
Database migration script to add detailed vehicle fields (metric units) to the vehicles table.
Run this script once after updating models.py.
"""
import sqlite3

DB_PATH = "myguardian.db"

ALTERS = [
    # New columns for vehicle details (metric units)
    ("make", "TEXT"),
    ("model", "TEXT"),
    ("year", "INTEGER"),
    ("vin", "TEXT"),
    ("license_plate", "TEXT"),
    ("mileage_km", "REAL"),
    ("fuel_capacity_liters", "REAL"),
    ("pump_capacity_lpm", "REAL"),
    ("ladder_height_m", "REAL"),
    ("water_tank_capacity_liters", "REAL"),
    ("foam_tank_capacity_liters", "REAL"),
    ("equipment_details", "TEXT"),
    ("purchase_date", "TEXT"),  # Store as ISO string
    ("warranty_expiration", "TEXT"),
    ("insurance_policy", "TEXT"),
    ("notes", "TEXT"),
]

def column_exists(cursor, table, column):
    cursor.execute(f"PRAGMA table_info({table})")
    return any(row[1] == column for row in cursor.fetchall())

def main():
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    for col, coltype in ALTERS:
        if not column_exists(cursor, "vehicles", col):
            print(f"Adding column: {col} ({coltype})")
            cursor.execute(f"ALTER TABLE vehicles ADD COLUMN {col} {coltype}")
        else:
            print(f"Column already exists: {col}")
    conn.commit()
    conn.close()
    print("Vehicle table migration complete.")

if __name__ == "__main__":
    main()
