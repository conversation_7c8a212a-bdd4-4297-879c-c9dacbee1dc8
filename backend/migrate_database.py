#!/usr/bin/env python3
"""
Database migration script to add response report fields and password change tracking.
Run this script to update your database with the new fields.

Usage:
    python migrate_database.py
"""

import sys
import os
from sqlalchemy import text, inspect
from database import engine, get_db
from models import Base

def check_column_exists(table_name, column_name):
    """Check if a column exists in a table"""
    inspector = inspect(engine)
    columns = [col['name'] for col in inspector.get_columns(table_name)]
    return column_name in columns

def add_column_if_not_exists(table_name, column_name, column_definition):
    """Add a column to a table if it doesn't already exist"""
    if not check_column_exists(table_name, column_name):
        with engine.connect() as conn:
            conn.execute(text(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_definition}"))
            conn.commit()
        print(f"✓ Added column '{column_name}' to table '{table_name}'")
    else:
        print(f"- Column '{column_name}' already exists in table '{table_name}'")

def migrate_database():
    """Run the database migration"""
    print("Starting database migration...")
    print("=" * 50)
    
    try:
        # Create all tables first (in case they don't exist)
        print("Creating tables if they don't exist...")
        Base.metadata.create_all(bind=engine)
        print("✓ Tables created/verified")
        
        # Add has_changed_default_password to users table
        print("\n1. Adding password change tracking to users table...")
        add_column_if_not_exists('users', 'has_changed_default_password', 'BOOLEAN DEFAULT FALSE')
        
        # Update existing users to have has_changed_default_password = TRUE
        with engine.connect() as conn:
            result = conn.execute(text("""
                UPDATE users 
                SET has_changed_default_password = TRUE 
                WHERE has_changed_default_password IS NULL OR has_changed_default_password = FALSE
            """))
            conn.commit()
            print(f"✓ Updated {result.rowcount} existing users to mark passwords as changed")
        
        # Add fire response report fields to emergencies table
        print("\n2. Adding fire response report fields to emergencies table...")
        
        report_fields = [
            ('cause_determined', 'VARCHAR(50)'),
            ('cause_description', 'TEXT'),
            ('water_used_liters', 'FLOAT'),
            ('fuel_used_liters', 'FLOAT'),
            ('personnel_count', 'INTEGER'),
            ('response_duration_minutes', 'INTEGER'),
            ('property_damage_estimate', 'FLOAT'),
            ('injuries_count', 'INTEGER DEFAULT 0'),
            ('fatalities_count', 'INTEGER DEFAULT 0'),
            ('additional_notes', 'TEXT'),
            ('resolved_by_user_id', 'INTEGER')
        ]
        
        for field_name, field_type in report_fields:
            add_column_if_not_exists('emergencies', field_name, field_type)
        
        # Verify the migration
        print("\n3. Verifying migration...")
        
        # Check users table
        if check_column_exists('users', 'has_changed_default_password'):
            with engine.connect() as conn:
                result = conn.execute(text("SELECT COUNT(*) as count FROM users WHERE has_changed_default_password = TRUE"))
                count = result.fetchone()[0]
                print(f"✓ Users table: {count} users have password change flag set")
        
        # Check emergencies table
        report_fields_check = ['cause_determined', 'water_used_liters', 'resolved_by_user_id']
        missing_fields = []
        for field in report_fields_check:
            if not check_column_exists('emergencies', field):
                missing_fields.append(field)
        
        if not missing_fields:
            print("✓ Emergencies table: All report fields added successfully")
        else:
            print(f"⚠ Emergencies table: Missing fields: {missing_fields}")
        
        print("\n" + "=" * 50)
        print("✅ Database migration completed successfully!")
        print("\nNew features available:")
        print("- Password change enforcement for new users")
        print("- Detailed fire response reports with metrics")
        print("- Team reassignment tracking")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {str(e)}")
        print("\nPlease check your database connection and try again.")
        sys.exit(1)

def rollback_migration():
    """Rollback the migration (remove added columns)"""
    print("Rolling back database migration...")
    print("=" * 50)
    
    try:
        # Remove columns from emergencies table
        report_fields = [
            'resolved_by_user_id', 'additional_notes', 'fatalities_count', 
            'injuries_count', 'property_damage_estimate', 'response_duration_minutes',
            'personnel_count', 'fuel_used_liters', 'water_used_liters',
            'cause_description', 'cause_determined'
        ]
        
        for field in report_fields:
            if check_column_exists('emergencies', field):
                with engine.connect() as conn:
                    conn.execute(text(f"ALTER TABLE emergencies DROP COLUMN {field}"))
                    conn.commit()
                print(f"✓ Removed column '{field}' from emergencies table")
        
        # Remove column from users table
        if check_column_exists('users', 'has_changed_default_password'):
            with engine.connect() as conn:
                conn.execute(text("ALTER TABLE users DROP COLUMN has_changed_default_password"))
                conn.commit()
            print("✓ Removed column 'has_changed_default_password' from users table")
        
        print("\n✅ Rollback completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Rollback failed: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--rollback":
        rollback_migration()
    else:
        migrate_database()
