#!/usr/bin/env python3
"""
Create comprehensive sample data for Malawi Fire Emergency System
Includes teams, vehicles, drivers, and responders with Malawi context
"""

from database import get_db
from models import (
    User, UserRole, Vehicle, VehicleStatus, Team, TeamMember, 
    VehicleDriver, FireStation, Emergency, EmergencyStatus, UrgencyLevel
)
from auth import hash_password
from sqlalchemy.orm import Session
import uuid
from datetime import datetime, timedelta
import random

def create_malawi_sample_data():
    """Create comprehensive sample data for Malawi context"""
    print("🇲🇼 Creating Malawi Fire Emergency System Sample Data...")
    
    db = next(get_db())
    
    try:
        # Malawi names and context
        malawi_names = [
            "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Mwale", "James Chi<PERSON>",
            "<PERSON> Tembo", "<PERSON>yi<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
            "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
        ]
        
        # Create fire stations for major Malawi cities
        stations_data = [
            {"name": "Lilongwe Central Fire Station", "region": "Central", "district": "Lilongwe", 
             "lat": -13.9626, "lng": 33.7741, "address": "Capital Hill, Lilongwe"},
            {"name": "Blantyre Fire Station", "region": "Southern", "district": "Blantyre",
             "lat": -15.7861, "lng": 35.0058, "address": "Victoria Avenue, Blantyre"},
            {"name": "Mzuzu Fire Station", "region": "Northern", "district": "Mzuzu",
             "lat": -11.4607, "lng": 34.0142, "address": "Orton Chirwa Avenue, Mzuzu"}
        ]
        
        stations = []
        for i, station_data in enumerate(stations_data, 1):
            existing_station = db.query(FireStation).filter(FireStation.id == i).first()
            if not existing_station:
                station = FireStation(
                    id=i,
                    name=station_data["name"],
                    latitude=station_data["lat"],
                    longitude=station_data["lng"],
                    region=station_data["region"],
                    district=station_data["district"],
                    address=station_data["address"],
                    phone_number=f"+265 1 {770000 + i}000"
                )
                db.add(station)
                stations.append(station)
            else:
                stations.append(existing_station)
        
        db.commit()
        print(f"✅ Created {len(stations)} fire stations")
        
        # Create vehicles for each station
        vehicle_types = ["Fire Engine", "Ambulance", "Rescue Vehicle", "Water Tanker"]
        vehicles = []
        
        vehicle_counter = 1
        for station in stations:
            for i in range(3):  # 3 vehicles per station
                vehicle_number = f"{station.district[:3].upper()}-{vehicle_counter:02d}"
                existing_vehicle = db.query(Vehicle).filter(
                    Vehicle.vehicle_number == vehicle_number
                ).first()

                if not existing_vehicle:
                    vehicle = Vehicle(
                        vehicle_number=vehicle_number,
                        vehicle_type=random.choice(vehicle_types),
                        status=VehicleStatus.AVAILABLE,
                        station_id=station.id,
                        make="Isuzu" if i % 2 == 0 else "Toyota",
                        model="NPR" if i % 2 == 0 else "Hiace",
                        year=random.randint(2015, 2023),
                        water_tank_capacity_liters=random.randint(2000, 5000),
                        fuel_capacity_liters=random.randint(80, 150)
                    )
                    db.add(vehicle)
                    vehicles.append(vehicle)
                else:
                    vehicles.append(existing_vehicle)
                vehicle_counter += 1
        
        db.commit()
        print(f"✅ Created {len(vehicles)} vehicles")
        
        # Create teams with military-style names
        team_names = ["Alpha", "Bravo", "Charlie", "Delta", "Echo", "Foxtrot"]
        teams = []
        
        for station in stations:
            for i, team_name in enumerate(team_names[:2]):  # 2 teams per station
                existing_team = db.query(Team).filter(
                    Team.name == f"{team_name} Team",
                    Team.station_id == station.id
                ).first()
                
                if not existing_team:
                    # Find station manager to create team
                    station_manager = db.query(User).filter(
                        User.role == UserRole.STATION_MANAGER,
                        User.station_id == station.id
                    ).first()
                    
                    if not station_manager:
                        # Create station manager if doesn't exist
                        manager_id = str(uuid.uuid4())
                        station_manager = User(
                            id=manager_id,
                            email=f"manager.{station.district.lower()}@fireservice.mw",
                            full_name=f"Station Manager {station.district}",
                            hashed_password=hash_password("manager123"),
                            role=UserRole.STATION_MANAGER,
                            phone_number=f"+265 99 {random.randint(1000000, 9999999)}",
                            station_id=station.id,
                            is_verified=True,
                            has_changed_default_password=True
                        )
                        db.add(station_manager)
                        db.commit()
                    
                    team = Team(
                        name=f"{team_name} Team",
                        station_id=station.id,
                        created_by=station_manager.id
                    )
                    db.add(team)
                    teams.append(team)
                else:
                    teams.append(existing_team)
        
        db.commit()
        print(f"✅ Created {len(teams)} teams")
        
        # Create drivers
        drivers = []
        for i, name in enumerate(malawi_names[:6]):  # 6 drivers
            driver_id = str(uuid.uuid4())
            station_id = (i % 3) + 1  # Distribute across stations
            
            existing_driver = db.query(User).filter(
                User.email == f"driver{i+1}@fireservice.mw"
            ).first()
            
            if not existing_driver:
                driver = User(
                    id=driver_id,
                    email=f"driver{i+1}@fireservice.mw",
                    full_name=name,
                    hashed_password=hash_password("driver123"),
                    role=UserRole.DRIVER,
                    phone_number=f"+265 88 {random.randint(1000000, 9999999)}",
                    station_id=station_id,
                    is_verified=True,
                    has_changed_default_password=True
                )
                db.add(driver)
                drivers.append(driver)
            else:
                drivers.append(existing_driver)
        
        db.commit()
        print(f"✅ Created {len(drivers)} drivers")
        
        # Create responders
        responders = []
        for i, name in enumerate(malawi_names[6:16]):  # 10 responders
            responder_id = str(uuid.uuid4())
            station_id = (i % 3) + 1  # Distribute across stations
            
            existing_responder = db.query(User).filter(
                User.email == f"responder{i+1}@fireservice.mw"
            ).first()
            
            if not existing_responder:
                responder = User(
                    id=responder_id,
                    email=f"responder{i+1}@fireservice.mw",
                    full_name=name,
                    hashed_password=hash_password("responder123"),
                    role=UserRole.RESPONDER,
                    phone_number=f"+265 99 {random.randint(1000000, 9999999)}",
                    station_id=station_id,
                    is_verified=True,
                    has_changed_default_password=True
                )
                db.add(responder)
                responders.append(responder)
            else:
                responders.append(existing_responder)
        
        db.commit()
        print(f"✅ Created {len(responders)} responders")
        
        # Assign drivers to vehicles
        vehicle_assignments = []
        for i, driver in enumerate(drivers):
            # Each driver gets assigned to 1-2 vehicles
            station_vehicles = [v for v in vehicles if v.station_id == driver.station_id]
            assigned_vehicles = station_vehicles[:2]  # Assign to first 2 vehicles
            
            for vehicle in assigned_vehicles:
                existing_assignment = db.query(VehicleDriver).filter(
                    VehicleDriver.vehicle_id == vehicle.id,
                    VehicleDriver.driver_id == driver.id
                ).first()
                
                if not existing_assignment:
                    assignment = VehicleDriver(
                        vehicle_id=vehicle.id,
                        driver_id=driver.id
                    )
                    db.add(assignment)
                    vehicle_assignments.append(assignment)
        
        db.commit()
        print(f"✅ Created {len(vehicle_assignments)} vehicle-driver assignments")
        
        # Assign responders to teams
        team_assignments = []
        for team in teams:
            # Get responders from same station
            station_responders = [r for r in responders if r.station_id == team.station_id]
            
            # Assign 3-4 responders per team
            team_responders = station_responders[:4]
            
            for i, responder in enumerate(team_responders):
                existing_member = db.query(TeamMember).filter(
                    TeamMember.team_id == team.id,
                    TeamMember.user_id == responder.id
                ).first()
                
                if not existing_member:
                    role = "leader" if i == 0 else "member"
                    member = TeamMember(
                        team_id=team.id,
                        user_id=responder.id,
                        role=role
                    )
                    db.add(member)
                    team_assignments.append(member)
        
        db.commit()
        print(f"✅ Created {len(team_assignments)} team member assignments")
        
        # Create sample emergencies
        emergency_locations = [
            {"lat": -13.9626, "lng": 33.7741, "desc": "Capital Hill area fire"},
            {"lat": -15.7861, "lng": 35.0058, "desc": "Blantyre market fire"},
            {"lat": -11.4607, "lng": 34.0142, "desc": "Mzuzu residential fire"}
        ]
        
        emergencies = []
        for i, location in enumerate(emergency_locations):
            emergency = Emergency(
                device_id=f"sensor_{i+1}",
                latitude=location["lat"],
                longitude=location["lng"],
                smoke_level=random.uniform(0.3, 0.9),
                urgency_level=random.choice([UrgencyLevel.LOW, UrgencyLevel.MEDIUM, UrgencyLevel.HIGH]),
                status=EmergencyStatus.ACTIVE,
                station_id=(i % 3) + 1
            )
            db.add(emergency)
            emergencies.append(emergency)
        
        db.commit()
        print(f"✅ Created {len(emergencies)} sample emergencies")
        
        print("\n🎉 Malawi Fire Emergency System sample data created successfully!")
        print("\n📊 Summary:")
        print(f"   🏢 Fire Stations: {len(stations)}")
        print(f"   🚒 Vehicles: {len(vehicles)}")
        print(f"   👥 Teams: {len(teams)}")
        print(f"   🚗 Drivers: {len(drivers)}")
        print(f"   🚨 Responders: {len(responders)}")
        print(f"   🔥 Sample Emergencies: {len(emergencies)}")
        
        print("\n🔑 Login Credentials:")
        print("   Station Managers:")
        for station in stations:
            print(f"     {station.district}: manager.{station.district.lower()}@fireservice.mw / manager123")
        print("   Drivers: <EMAIL> / driver123")
        print("   Responders: <EMAIL> / responder123")
        
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    create_malawi_sample_data()
