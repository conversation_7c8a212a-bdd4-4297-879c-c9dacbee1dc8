from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, ForeignKey, Enum, Text
from sqlalchemy.orm import relationship
from database import Base
from datetime import datetime
import enum

# Enums
class UserRole(str, enum.Enum):
    ADMIN = "admin"
    USER = "user"
    RESPONDER = 'responder'
    STATION_MANAGER = "station_manager"
    DRIVER = "driver"

class EmergencyStatus(str, enum.Enum):
    ACTIVE = "active"
    DISPATCHED = "dispatched"
    RESOLVED = "resolved"

class UrgencyLevel(str, enum.Enum):
    LOW = "low"
    MEDIUM = "medium" 
    HIGH = "high"

class VehicleStatus(str, enum.Enum):
    AVAILABLE = "available"
    DISPATCHED = "dispatched"
    MAINTENANCE = "maintenance"

# Database Models
class User(Base):
    __tablename__ = "users"
    
    id = Column(String, primary_key=True, index=True)  # UUID as string
    email = Column(String, unique=True, index=True)
    full_name = Column(String)
    hashed_password = Column(String)
    role = Column(Enum(UserRole))
    phone_number = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    station_id = Column(Integer, ForeignKey("fire_stations.id"), nullable=True)
    is_verified = Column(Boolean, default=False)
    has_changed_default_password = Column(Boolean, default=False)

    # Additional fields for mobile app compatibility
    department = Column(String, nullable=True)
    username = Column(String, nullable=True)
    first_name = Column(String, nullable=True)
    last_name = Column(String, nullable=True)
    is_superuser = Column(Boolean, default=False)
    is_staff = Column(Boolean, default=False)
    date_joined = Column(DateTime, default=datetime.utcnow)
    badge_number = Column(String, nullable=True)
    rank = Column(String, default="User")
    years_of_service = Column(Integer, default=0)
    certifications = Column(String, default="None")
    emergency_contact_name = Column(String, default="N/A")
    emergency_contact_phone = Column(String, default="**********")
    medical_conditions = Column(String, default="None")
    medications = Column(String, default="None")
    allergies = Column(String, default="None")
    blood_type = Column(String, default="O+")
    is_active_user = Column(Boolean, default=True)
    updated_at = Column(DateTime, default=datetime.utcnow)
    employee_id = Column(String, nullable=True)
    vehicle_id = Column(Integer, ForeignKey("vehicles.id"), nullable=True)  # Responder's assigned vehicle
    
    station = relationship("FireStation", back_populates="managers")
    devices = relationship("Device", back_populates="user")
    vehicle = relationship("Vehicle", foreign_keys=[vehicle_id], backref="responders")
    team_memberships = relationship("TeamMember", back_populates="user")
    vehicle_assignments = relationship("VehicleDriver", back_populates="driver")

class FireStation(Base):
    __tablename__ = "fire_stations"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    latitude = Column(Float)
    longitude = Column(Float)
    region = Column(String)
    district = Column(String)
    address = Column(String)
    phone_number = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    managers = relationship("User", back_populates="station")
    vehicles = relationship("Vehicle", back_populates="station")
    teams = relationship("Team", back_populates="station")

class Vehicle(Base):
    __tablename__ = "vehicles"
    
    id = Column(Integer, primary_key=True, index=True)
    vehicle_number = Column(String, unique=True, index=True)
    vehicle_type = Column(String)  # Fire truck, ambulance, etc.
    status = Column(Enum(VehicleStatus), default=VehicleStatus.AVAILABLE)
    station_id = Column(Integer, ForeignKey("fire_stations.id"))
    created_at = Column(DateTime, default=datetime.utcnow)

    # Expanded details (metric units)
    make = Column(String, nullable=True)
    model = Column(String, nullable=True)
    year = Column(Integer, nullable=True)
    vin = Column(String, nullable=True)
    license_plate = Column(String, nullable=True)
    mileage_km = Column(Float, nullable=True)  # kilometers
    fuel_capacity_liters = Column(Float, nullable=True)
    pump_capacity_lpm = Column(Float, nullable=True)  # liters per minute
    ladder_height_m = Column(Float, nullable=True)  # meters
    water_tank_capacity_liters = Column(Float, nullable=True)
    foam_tank_capacity_liters = Column(Float, nullable=True)
    equipment_details = Column(String, nullable=True)
    purchase_date = Column(DateTime, nullable=True)
    warranty_expiration = Column(DateTime, nullable=True)
    insurance_policy = Column(String, nullable=True)
    notes = Column(String, nullable=True)

    station = relationship("FireStation", back_populates="vehicles")
    dispatches = relationship("EmergencyDispatch", back_populates="vehicle")
    drivers = relationship("VehicleDriver", back_populates="vehicle")
    duty_shifts = relationship("DutyShift", back_populates="vehicle")

class Device(Base):
    __tablename__ = "devices"
    
    id = Column(String, primary_key=True, index=True)  # UUID as string
    mac_address = Column(String, unique=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))  # String to match User.id
    device_name = Column(String)
    is_active = Column(Boolean, default=True)
    paired_at = Column(DateTime, default=datetime.utcnow)
    
    user = relationship("User", back_populates="devices")
    readings = relationship("SensorReading", back_populates="device")

class SensorReading(Base):
    __tablename__ = "sensor_readings"
    
    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, ForeignKey("devices.id"))  # String to match Device.id
    mac_address = Column(String, index=True)
    latitude = Column(Float)
    longitude = Column(Float)
    smoke_level = Column(Float)
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    device = relationship("Device", back_populates="readings")

class Emergency(Base):
    __tablename__ = "emergencies"
    
    id = Column(Integer, primary_key=True, index=True)
    device_id = Column(String, ForeignKey("devices.id"))  # String to match Device.id
    latitude = Column(Float)
    longitude = Column(Float)
    smoke_level = Column(Float)
    urgency_level = Column(Enum(UrgencyLevel))
    status = Column(Enum(EmergencyStatus), default=EmergencyStatus.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow)
    resolved_at = Column(DateTime, nullable=True)

    # Fire response report fields
    cause_determined = Column(String(50), nullable=True)  # yes, no, under_investigation
    cause_description = Column(Text, nullable=True)
    water_used_liters = Column(Float, nullable=True)
    fuel_used_liters = Column(Float, nullable=True)
    personnel_count = Column(Integer, nullable=True)
    response_duration_minutes = Column(Integer, nullable=True)
    property_damage_estimate = Column(Float, nullable=True)
    injuries_count = Column(Integer, nullable=True, default=0)
    fatalities_count = Column(Integer, nullable=True, default=0)
    additional_notes = Column(Text, nullable=True)
    resolved_by_user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    station_id = Column(Integer, ForeignKey("fire_stations.id"), nullable=True)

    # Assignment fields
    assigned_team_id = Column(Integer, ForeignKey("teams.id"), nullable=True)
    assigned_driver_id = Column(String, ForeignKey("users.id"), nullable=True)
    assigned_vehicle_id = Column(Integer, ForeignKey("vehicles.id"), nullable=True)

    dispatches = relationship("EmergencyDispatch", back_populates="emergency")
    assigned_team = relationship("Team", foreign_keys=[assigned_team_id])
    assigned_driver = relationship("User", foreign_keys=[assigned_driver_id])
    assigned_vehicle = relationship("Vehicle", foreign_keys=[assigned_vehicle_id])

class EmergencyDispatch(Base):
    __tablename__ = "emergency_dispatches"
    
    id = Column(Integer, primary_key=True, index=True)
    emergency_id = Column(Integer, ForeignKey("emergencies.id"))
    vehicle_id = Column(Integer, ForeignKey("vehicles.id"))
    station_id = Column(Integer, ForeignKey("fire_stations.id"))
    dispatched_at = Column(DateTime, default=datetime.utcnow)
    
    emergency = relationship("Emergency", back_populates="dispatches")
    vehicle = relationship("Vehicle", back_populates="dispatches")

class EmergencyContact(Base):
    __tablename__ = "emergency_contacts"
    
    id = Column(Integer, primary_key=True, index=True)
    contact_id = Column(String, unique=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    name = Column(String)
    phone_number = Column(String)
    relation = Column(String)
    preferred_method = Column(String, default="phone")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)

class VerificationCode(Base):
    __tablename__ = "verification_codes"
    
    id = Column(Integer, primary_key=True, index=True)
    phone_number = Column(String, index=True)
    code = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    is_used = Column(Boolean, default=False)
    expires_at = Column(DateTime)

class WaterRefillPoint(Base):
    __tablename__ = "water_refill_points"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=True)
    latitude = Column(Float, nullable=False)
    longitude = Column(Float, nullable=False)
    description = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)

class Team(Base):
    __tablename__ = "teams"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    station_id = Column(Integer, ForeignKey("fire_stations.id"))
    created_by = Column(String, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)

    station = relationship("FireStation", back_populates="teams")
    members = relationship("TeamMember", back_populates="team")
    duty_shifts = relationship("DutyShift", back_populates="team")

class TeamMember(Base):
    __tablename__ = "team_members"

    id = Column(Integer, primary_key=True, index=True)
    team_id = Column(Integer, ForeignKey("teams.id"))
    user_id = Column(String, ForeignKey("users.id"))
    role = Column(String, default="member")  # leader, member
    joined_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)

    team = relationship("Team", back_populates="members")
    user = relationship("User", back_populates="team_memberships")

class DutyShift(Base):
    __tablename__ = "duty_shifts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String, ForeignKey("users.id"))
    team_id = Column(Integer, ForeignKey("teams.id"), nullable=True)
    vehicle_id = Column(Integer, ForeignKey("vehicles.id"), nullable=True)
    shift_start = Column(DateTime, default=datetime.utcnow)
    shift_end = Column(DateTime, nullable=True)
    status = Column(String, default="on_duty")  # on_duty, off_duty
    clocked_in_by = Column(String, ForeignKey("users.id"))

    user = relationship("User", foreign_keys=[user_id])
    team = relationship("Team", back_populates="duty_shifts")
    vehicle = relationship("Vehicle", back_populates="duty_shifts")
    clocked_in_by_user = relationship("User", foreign_keys=[clocked_in_by])

class VehicleDriver(Base):
    __tablename__ = "vehicle_drivers"

    id = Column(Integer, primary_key=True, index=True)
    vehicle_id = Column(Integer, ForeignKey("vehicles.id"))
    driver_id = Column(String, ForeignKey("users.id"))
    assigned_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)

    vehicle = relationship("Vehicle", back_populates="drivers")
    driver = relationship("User", back_populates="vehicle_assignments")