#!/bin/bash

# Database Migration Script for Fire Emergency System
# This script updates the database with new fields for response reports and password tracking

echo "🔥 Fire Emergency System - Database Migration"
echo "=============================================="

# Check if we're in the backend directory
if [ ! -f "migrate_database.py" ]; then
    echo "❌ Error: Please run this script from the backend directory"
    echo "   cd backend && ./run_migration.sh"
    exit 1
fi

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Error: Python 3 is required but not installed"
    exit 1
fi

# Check if virtual environment exists and activate it
if [ -d "venv" ]; then
    echo "📦 Activating virtual environment..."
    source venv/bin/activate
elif [ -d "../venv" ]; then
    echo "📦 Activating virtual environment..."
    source ../venv/bin/activate
else
    echo "⚠️  Warning: No virtual environment found. Using system Python."
fi

# Check if required packages are installed
echo "🔍 Checking dependencies..."
python3 -c "import sqlalchemy, fastapi" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ Error: Required packages not installed. Please run:"
    echo "   pip install -r requirements.txt"
    exit 1
fi

# Run the migration
echo ""
echo "🚀 Running database migration..."
echo ""

if [ "$1" = "--rollback" ]; then
    echo "⚠️  ROLLBACK MODE: This will remove the new fields!"
    read -p "Are you sure you want to rollback? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        python3 migrate_database.py --rollback
    else
        echo "Rollback cancelled."
        exit 0
    fi
else
    python3 migrate_database.py
fi

# Check exit status
if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 Migration completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Restart your FastAPI backend server"
    echo "2. Test the new features:"
    echo "   - Profile management with password changes"
    echo "   - Enhanced dashboards with real data"
    echo "   - Team reassignment functionality"
    echo "   - Fire response reports"
    echo ""
    echo "💡 Tip: New users will be prompted to change their default passwords"
else
    echo ""
    echo "❌ Migration failed. Please check the error messages above."
    exit 1
fi
