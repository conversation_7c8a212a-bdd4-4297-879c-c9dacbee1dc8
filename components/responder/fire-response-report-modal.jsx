"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";

export function FireResponseReportModal({
  isOpen,
  onClose,
  emergency,
  onSuccess,
}) {
  const [formData, setFormData] = useState({
    cause_determined: "",
    cause_description: "",
    water_used_liters: "",
    fuel_used_liters: "",
    personnel_count: "",
    response_duration_minutes: "",
    property_damage_estimate: "",
    injuries_count: "0",
    fatalities_count: "0",
    additional_notes: "",
  });
  const [loading, setLoading] = useState(false);

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async () => {
    // Validate required fields
    if (!formData.cause_determined) {
      toast.error("Please specify if the cause was determined");
      return;
    }

    setLoading(true);
    try {
      const token = localStorage.getItem("token");

      // Prepare the payload
      const payload = {
        emergency_id: emergency.id,
        cause_determined: formData.cause_determined,
        cause_description: formData.cause_description || null,
        water_used_liters: formData.water_used_liters
          ? parseFloat(formData.water_used_liters)
          : null,
        fuel_used_liters: formData.fuel_used_liters
          ? parseFloat(formData.fuel_used_liters)
          : null,
        personnel_count: formData.personnel_count
          ? parseInt(formData.personnel_count)
          : null,
        response_duration_minutes: formData.response_duration_minutes
          ? parseInt(formData.response_duration_minutes)
          : null,
        property_damage_estimate: formData.property_damage_estimate
          ? parseFloat(formData.property_damage_estimate)
          : null,
        injuries_count: parseInt(formData.injuries_count) || 0,
        fatalities_count: parseInt(formData.fatalities_count) || 0,
        additional_notes: formData.additional_notes || null,
      };

      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";
      const response = await fetch(`${backendUrl}/api/emergencies/resolve`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        toast.success("Emergency resolved and report submitted successfully");
        onSuccess();
        onClose();
        resetForm();
      } else {
        const error = await response.json();
        toast.error(error.detail || "Failed to submit report");
      }
    } catch (error) {
      toast.error("Failed to submit report");
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      cause_determined: "",
      cause_description: "",
      water_used_liters: "",
      fuel_used_liters: "",
      personnel_count: "",
      response_duration_minutes: "",
      property_damage_estimate: "",
      injuries_count: "0",
      fatalities_count: "0",
      additional_notes: "",
    });
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  if (!emergency) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Fire Response Report</DialogTitle>
          <DialogDescription>
            Complete the fire response report for Emergency #{emergency.id}{" "}
            before marking it as resolved.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="cause-determined">Cause Determined *</Label>
              <Select
                value={formData.cause_determined}
                onValueChange={(value) =>
                  handleInputChange("cause_determined", value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="yes">Yes</SelectItem>
                  <SelectItem value="no">No</SelectItem>
                  <SelectItem value="under_investigation">
                    Under Investigation
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="personnel-count">Personnel Count</Label>
              <Input
                id="personnel-count"
                type="number"
                placeholder="Number of personnel"
                value={formData.personnel_count}
                onChange={(e) =>
                  handleInputChange("personnel_count", e.target.value)
                }
              />
            </div>
          </div>

          {formData.cause_determined === "yes" && (
            <div className="grid gap-2">
              <Label htmlFor="cause-description">Cause Description</Label>
              <Textarea
                id="cause-description"
                placeholder="Describe the determined cause..."
                value={formData.cause_description}
                onChange={(e) =>
                  handleInputChange("cause_description", e.target.value)
                }
                rows={3}
              />
            </div>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="water-used">Water Used (Liters)</Label>
              <Input
                id="water-used"
                type="number"
                step="0.1"
                placeholder="Liters of water used"
                value={formData.water_used_liters}
                onChange={(e) =>
                  handleInputChange("water_used_liters", e.target.value)
                }
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="fuel-used">Fuel Used (Liters)</Label>
              <Input
                id="fuel-used"
                type="number"
                step="0.1"
                placeholder="Liters of fuel used"
                value={formData.fuel_used_liters}
                onChange={(e) =>
                  handleInputChange("fuel_used_liters", e.target.value)
                }
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="response-duration">
                Response Duration (Minutes)
              </Label>
              <Input
                id="response-duration"
                type="number"
                placeholder="Total response time"
                value={formData.response_duration_minutes}
                onChange={(e) =>
                  handleInputChange("response_duration_minutes", e.target.value)
                }
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="property-damage">
                Property Damage Estimate (€)
              </Label>
              <Input
                id="property-damage"
                type="number"
                step="0.01"
                placeholder="Estimated damage in euros"
                value={formData.property_damage_estimate}
                onChange={(e) =>
                  handleInputChange("property_damage_estimate", e.target.value)
                }
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="injuries">Injuries Count</Label>
              <Input
                id="injuries"
                type="number"
                min="0"
                value={formData.injuries_count}
                onChange={(e) =>
                  handleInputChange("injuries_count", e.target.value)
                }
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="fatalities">Fatalities Count</Label>
              <Input
                id="fatalities"
                type="number"
                min="0"
                value={formData.fatalities_count}
                onChange={(e) =>
                  handleInputChange("fatalities_count", e.target.value)
                }
              />
            </div>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="additional-notes">Additional Notes</Label>
            <Textarea
              id="additional-notes"
              placeholder="Any additional observations or notes..."
              value={formData.additional_notes}
              onChange={(e) =>
                handleInputChange("additional_notes", e.target.value)
              }
              rows={4}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading || !formData.cause_determined}
          >
            {loading ? "Submitting..." : "Submit Report & Resolve"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
