"use client";

import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import {
  LayoutDashboard,
  Users,
  Building2,
  User,
  AlertTriangle,
  Home,
  LogOut,
  Droplet,
} from "lucide-react";
import Link from "next/link";

const menuItems = [
  {
    title: "Dashboard",
    url: "/admin",
    icon: LayoutDashboard,
  },
  {
    title: "User Management",
    url: "/admin/users",
    icon: Users,
  },
  {
    title: "Station Management",
    url: "/admin/stations",
    icon: Building2,
  },
  {
    title: "Emergencies",
    url: "/admin/emergencies",
    icon: AlertTriangle,
  },
  {
    title: "Water Refill",
    url: "/admin/refill-points",
    icon: Droplet,
  },
];

export function AdminSidebar() {
  return (
    <Sidebar>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Administration</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <Link href={item.url}>
                      <item.icon className="w-4 h-4" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      {/* Footer */}
      <SidebarFooter className="py-4 mb-2">
        <SidebarMenuButton asChild>
          <Link href="/admin/profile">
            <User className="w-4 h-4" />
            <span>Profile</span>
          </Link>
        </SidebarMenuButton>
        <SidebarMenuButton
          asChild
          className="bg-red-500 hover:bg-red-600 text-white hover:text-gray-600"
        >
          <Link href="/logout">
            <LogOut className="w-4 h-4" />
            <span>Logout</span>
          </Link>
        </SidebarMenuButton>
      </SidebarFooter>
    </Sidebar>
  );
}
