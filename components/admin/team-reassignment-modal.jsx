"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

export function TeamReassignmentModal({ isOpen, onClose, user, onSuccess }) {
  const [selectedStationId, setSelectedStationId] = useState("");
  const [reason, setReason] = useState("");
  const [stations, setStations] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchStations();
    }
  }, [isOpen]);

  const fetchStations = async () => {
    try {
      const token = localStorage.getItem("token");
      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";
      const response = await fetch(`${backendUrl}/api/admin/stations`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.ok) {
        const data = await response.json();
        setStations(data);
      }
    } catch (error) {
      toast.error("Failed to load stations");
    }
  };

  const handleReassign = async () => {
    if (!selectedStationId) {
      toast.error("Please select a station");
      return;
    }

    setLoading(true);
    try {
      const token = localStorage.getItem("token");
      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";
      const response = await fetch(
        `${backendUrl}/api/admin/reassign-responder`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            user_id: user.id,
            new_station_id: parseInt(selectedStationId),
            reason: reason || null,
          }),
        }
      );

      if (response.ok) {
        const data = await response.json();
        toast.success(data.message);
        onSuccess();
        onClose();
        setSelectedStationId("");
        setReason("");
      } else {
        const error = await response.json();
        toast.error(error.detail || "Failed to reassign responder");
      }
    } catch (error) {
      toast.error("Failed to reassign responder");
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedStationId("");
    setReason("");
    onClose();
  };

  if (!user) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Reassign Responder</DialogTitle>
          <DialogDescription>
            Reassign {user.full_name} to a different fire station.
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="current-station">Current Station</Label>
            <div className="p-2 bg-muted rounded-md text-sm">
              {user.station_name || "No station assigned"}
            </div>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="new-station">New Station</Label>
            <Select
              value={selectedStationId}
              onValueChange={setSelectedStationId}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a station" />
              </SelectTrigger>
              <SelectContent>
                {stations
                  .filter((station) => station.id !== user.station_id)
                  .map((station) => (
                    <SelectItem key={station.id} value={station.id.toString()}>
                      {station.name} - {station.district}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="reason">Reason (Optional)</Label>
            <Textarea
              id="reason"
              placeholder="Enter reason for reassignment..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            onClick={handleReassign}
            disabled={loading || !selectedStationId}
          >
            {loading ? "Reassigning..." : "Reassign"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
