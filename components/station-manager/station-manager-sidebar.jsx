"use client";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarFooter, // Import SidebarFooter
} from "@/components/ui/sidebar";
import {
  LayoutDashboard,
  Users,
  Truck,
  Plus,
  AlertTriangle,
  History,
  User,
  LogOut,
  Droplet,
} from "lucide-react";
import Link from "next/link";

const menuItems = [
  {
    title: "Dashboard",
    url: "/station-manager",
    icon: LayoutDashboard,
  },
  {
    title: "Personnel",
    url: "/station-manager/responders",
    icon: Users,
  },
  {
    title: "Teams",
    url: "/station-manager/teams",
    icon: Users,
  },
  {
    title: "Vehicles",
    url: "/station-manager/vehicles",
    icon: Truck,
  },
  {
    title: "Active Alerts",
    url: "/station-manager/alerts",
    icon: AlertTriangle,
  },
  {
    title: "Alert History",
    url: "/station-manager/alerts/history",
    icon: History,
  },
  {
    title: "Water Refill",
    url: "/station-manager/refill-points",
    icon: Droplet,
  },
];

export function StationManagerSidebar() {
  return (
    <Sidebar>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Station Management</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <Link href={item.url}>
                      <item.icon className="w-4 h-4" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <Link href="/station-manager/profile">
                <User className="w-4 h-4" />
                <span>Profile</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <Link
                href="/logout"
                style={{ backgroundColor: "red", color: "white" }}
              >
                <LogOut className="w-4 h-4" />
                <span>Logout</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
