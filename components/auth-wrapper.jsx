"use client";

import { useState, useEffect } from "react";
import { PasswordChangeModal } from "./password-change-modal";

export function AuthWrapper({ children }) {
  const [needsPasswordChange, setNeedsPasswordChange] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkPasswordChangeRequired();
  }, []);

  const checkPasswordChangeRequired = async () => {
    try {
      const token = localStorage.getItem("token");
      if (!token) {
        setLoading(false);
        return;
      }

      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";
      const response = await fetch(
        `${backendUrl}/api/profile/needs-password-change`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (response.ok) {
        const data = await response.json();
        setNeedsPasswordChange(data.needs_password_change);
      }
    } catch (error) {
      console.error("Failed to check password change requirement");
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChanged = () => {
    setNeedsPasswordChange(false);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        Loading...
      </div>
    );
  }

  return (
    <>
      {children}
      <PasswordChangeModal
        isOpen={needsPasswordChange}
        onClose={handlePasswordChanged}
        isRequired={true}
      />
    </>
  );
}
