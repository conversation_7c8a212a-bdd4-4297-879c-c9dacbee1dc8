"use client";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Building2,
  MapPin,
  Phone,
  Users,
  Truck,
  Calendar,
  Shield,
  ArrowLeft,
  Edit,
  Activity,
  AlertTriangle,
} from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";

export default function StationDetails({ params }) {
  const stationId = params.id;
  const [station, setStation] = useState(null);
  const [error, setError] = useState("");
  const router = useRouter();

  useEffect(() => {
    async function fetchStation() {
      try {
        // Use backendUrl if available, and fetch a single station by id
        const backendUrl =
          typeof window !== "undefined" && process.env.NEXT_PUBLIC_BACKEND_URL
            ? process.env.NEXT_PUBLIC_BACKEND_URL
            : "https://my-guardian-plus-1.onrender.com";
        const res = await fetch(`${backendUrl}/admin/stations/${stationId}`, {
          credentials: "include",
        });
        if (!res.ok) throw new Error("Failed to fetch station");
        const data = await res.json();
        setStation(data);
      } catch (e) {
        setError("Could not load station details.");
      }
    }
    fetchStation();
  }, [stationId]);

  if (error) return <div className="p-8 text-red-600">{error}</div>;
  if (!station) return <div className="p-8">Loading...</div>;

  const openGoogleMaps = () => {
    if (!station.latitude || !station.longitude) return;
    window.open(
      `https://maps.google.com/?q=${station.latitude},${station.longitude}`,
      "_blank"
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/admin/stations">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Stations
          </Button>
        </Link>
        <div className="flex-1">
          <h1 className="text-3xl font-bold">{station.name}</h1>
          <p className="text-muted-foreground">{station.address}</p>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="w-5 h-5" />
              Station Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">
                  District
                </h4>
                <p>{station.district}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">
                  Region
                </h4>
                <p>{station.region}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">
                  Phone
                </h4>
                <p>{station.phone_number}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">
                  Location
                </h4>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={openGoogleMaps}
                  className="flex items-center gap-2 bg-transparent"
                >
                  <MapPin className="w-4 h-4" />
                  View on Google Maps
                </Button>
              </div>
              {/* Station Manager Display */}
              {station.managers && station.managers.length > 0 && (
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">
                    Station Manager
                  </h4>
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-muted-foreground" />
                    <span className="font-medium">
                      {station.managers[0].full_name}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      ({station.managers[0].email})
                    </span>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
