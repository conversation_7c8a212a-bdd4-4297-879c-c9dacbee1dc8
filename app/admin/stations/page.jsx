"use client";

import { useState, useEffect, useRef } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  MapPin,
  Building2,
  Users,
  Truck,
  Eye,
} from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";

// Dynamic import for Leaflet components to avoid SSR issues
const DynamicMap = ({ coords, setCoords, onCoordinatesChange }) => {
  const [isClient, setIsClient] = useState(false);
  const mapRef = useRef(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Default to Mzuzu, Malawi coordinates
  const defaultCenter = [-11.4596, 34.0194]; // Mzuzu coordinates
  const defaultZoom = 15;

  useEffect(() => {
    if (!isClient || (!coords[0] && !coords[1])) return;

    const initializeMap = async () => {
      // Dynamically import Leaflet
      const L = (await import("leaflet")).default;

      // Fix for default markers
      delete L.Icon.Default.prototype._getIconUrl;
      L.Icon.Default.mergeOptions({
        iconRetinaUrl:
          "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png",
        iconUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png",
        shadowUrl:
          "https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png",
      });

      // Clear existing map
      if (mapRef.current) {
        mapRef.current.innerHTML = "";
      }

      // Create map - always center on Mzuzu initially
      const map = L.map(mapRef.current).setView(
        coords[0] && coords[1] ? coords : defaultCenter,
        coords[0] && coords[1] ? defaultZoom : 13 // Slightly zoomed out when showing Mzuzu
      );

      // Add tile layer
      L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
        attribution:
          '&copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a>',
      }).addTo(map);

      // Add marker if coordinates exist
      let marker;
      if (coords[0] && coords[1]) {
        marker = L.marker(coords).addTo(map);
      }

      // Handle map clicks
      map.on("click", (e) => {
        const { lat, lng } = e.latlng;
        setCoords([lat, lng]);

        // Remove existing marker
        if (marker) {
          map.removeLayer(marker);
        }

        // Add new marker
        marker = L.marker([lat, lng]).addTo(map);

        // Update coordinates input
        const coordsString = `${lat.toFixed(6)},${lng.toFixed(6)}`;
        if (onCoordinatesChange) {
          onCoordinatesChange(coordsString);
        }
      });

      // Cleanup function
      return () => {
        if (map) {
          map.remove();
        }
      };
    };

    initializeMap();
  }, [isClient, coords, setCoords, onCoordinatesChange]);

  if (!isClient) {
    return (
      <div
        className="w-full bg-gray-100 flex items-center justify-center"
        style={{ height: "250px", borderRadius: 8, marginTop: 8 }}
      >
        <p className="text-gray-500">Loading map...</p>
      </div>
    );
  }

  return (
    <>
      <link
        rel="stylesheet"
        href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      />
      <div
        ref={mapRef}
        style={{
          height: "250px",
          width: "100%",
          borderRadius: 8,
          overflow: "hidden",
          marginTop: 8,
        }}
      />
    </>
  );
};

const backendUrl = process.env.BACKEND_URL || "";

export default function StationManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRegion, setSelectedRegion] = useState("all");
  const [isAddStationOpen, setIsAddStationOpen] = useState(false);
  const [stations, setStations] = useState([]);
  const [regions, setRegions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [newStation, setNewStation] = useState({
    name: "",
    captain: "",
    address: "",
    district: "",
    region: "",
    phone: "",
    coordinates: "",
    established: "",
  });
  const [mapCoords, setMapCoords] = useState([null, null]);

  // Malawi regions and districts mapping
  const malawiRegions = ["North", "Central", "South"];

  const malawiRegionDistricts = {
    North: [
      "Chitipa",
      "Karonga",
      "Rumphi",
      "Nkhata Bay",
      "Likoma",
      "Mzimba",
      "Mzuzu",
    ],
    Central: [
      "Kasungu",
      "Nkhotakota",
      "Ntchisi",
      "Dowa",
      "Salima",
      "Lilongwe",
      "Mchinji",
      "Dedza",
      "Ntcheu",
    ],
    South: [
      "Mangochi",
      "Machinga",
      "Zomba",
      "Chiradzulu",
      "Blantyre",
      "Mwanza",
      "Thyolo",
      "Mulanje",
      "Phalombe",
      "Chikwawa",
      "Nsanje",
      "Balaka",
      "Neno",
    ],
  };

  useEffect(() => {
    async function fetchStations() {
      setLoading(true);
      try {
        const res = await fetch(`${backendUrl}/admin/stations`, {
          credentials: "include",
        });
        if (!res.ok) throw new Error("Failed to fetch stations");
        const data = await res.json();
        setStations(data);
        setRegions([...new Set(data.map((s) => s.region))]);
      } catch {
        setStations([]);
        setRegions([]);
      } finally {
        setLoading(false);
      }
    }
    fetchStations();
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "default";
      case "maintenance":
        return "secondary";
      case "inactive":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const filteredStations = stations.filter((station) => {
    const matchesSearch =
      station.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      station.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
      station.district.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRegion =
      selectedRegion === "all" || station.region === selectedRegion;
    return matchesSearch && matchesRegion;
  });

  const handleNewStationChange = (field, value) => {
    setNewStation((prev) => ({ ...prev, [field]: value }));
    if (field === "coordinates") {
      const [lat, lng] = value.split(",").map(Number);
      if (!isNaN(lat) && !isNaN(lng)) setMapCoords([lat, lng]);
    }
  };

  const handleAddStation = async () => {
    setLoading(true);
    try {
      const [latitude, longitude] = newStation.coordinates
        .split(",")
        .map(Number);
      const payload = {
        name: newStation.name,
        captain: newStation.captain,
        address: newStation.address,
        district: newStation.district,
        region: newStation.region,
        phone_number: newStation.phone,
        latitude,
        longitude,
        established: newStation.established,
        manager_name: newStation.manager_name || undefined,
        manager_email: newStation.manager_email || undefined,
        manager_phone: newStation.manager_phone || undefined,
      };
      const res = await fetch(`${backendUrl}/admin/stations`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        credentials: "include",
        body: JSON.stringify(payload),
      });
      if (!res.ok) throw new Error("Failed to add station");
      toast.success("Station added successfully");
      setIsAddStationOpen(false);
      setNewStation({
        name: "",
        captain: "",
        address: "",
        district: "",
        region: "",
        phone: "",
        coordinates: "",
        established: "",
      });
      setMapCoords([null, null]);
      const updated = await fetch(`${backendUrl}/admin/stations`, {
        credentials: "include",
      });
      setStations(await updated.json());
    } catch (e) {
      toast.error("Failed to add station");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteStation = (stationName) => {
    toast.success(`${stationName} deleted successfully`);
  };

  const openGoogleMaps = (coordinates) => {
    const [lat, lng] = coordinates.split(",");
    window.open(`https://maps.google.com/?q=${lat},${lng}`, "_blank");
  };

  const totalStations = stations.length;
  const activeStations = stations.filter((s) => s.status === "active").length;
  const totalPersonnel = stations.reduce(
    (sum, station) => sum + station.personnel,
    0
  );
  const totalVehicles = stations.reduce(
    (sum, station) => sum + station.vehicles,
    0
  );

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Station Management</h1>
        <p className="text-muted-foreground">
          Manage fire stations, their locations, personnel, and operational
          status
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Stations
            </CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStations}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Stations
            </CardTitle>
            <Building2 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {activeStations}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Personnel
            </CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPersonnel}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Vehicles
            </CardTitle>
            <Truck className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalVehicles}</div>
          </CardContent>
        </Card>
      </div>

      {/* Station Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Fire Stations</CardTitle>
              <CardDescription>
                Manage all fire stations and their operational details
              </CardDescription>
            </div>
            <Dialog open={isAddStationOpen} onOpenChange={setIsAddStationOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Station
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl overflow-y-auto max-h-screen">
                <DialogHeader>
                  <DialogTitle>Add New Fire Station</DialogTitle>
                  <DialogDescription>
                    Create a new fire station in the system
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="station-name">Station Name</Label>
                      <Input
                        id="station-name"
                        placeholder="Station 6"
                        value={newStation.name}
                        onChange={(e) =>
                          handleNewStationChange("name", e.target.value)
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="captain">Station Captain</Label>
                      <Input
                        id="captain"
                        placeholder="Captain Name"
                        value={newStation.captain}
                        onChange={(e) =>
                          handleNewStationChange("captain", e.target.value)
                        }
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      placeholder="123 Fire Station Rd, City, State"
                      value={newStation.address}
                      onChange={(e) =>
                        handleNewStationChange("address", e.target.value)
                      }
                    />
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    {/* Region Select */}
                    <div className="space-y-2">
                      <Label htmlFor="region">Region</Label>
                      <Select
                        value={newStation.region}
                        onValueChange={(value) => {
                          handleNewStationChange("region", value);
                          handleNewStationChange("district", ""); // reset district when region changes
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select region" />
                        </SelectTrigger>
                        <SelectContent>
                          {malawiRegions.map((region) => (
                            <SelectItem key={region} value={region}>
                              {region}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* District Select (depends on region) */}
                    <div className="space-y-2">
                      <Label htmlFor="district">District</Label>
                      <Select
                        value={newStation.district}
                        onValueChange={(value) =>
                          handleNewStationChange("district", value)
                        }
                        disabled={!newStation.region}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select district" />
                        </SelectTrigger>
                        <SelectContent>
                          {newStation.region &&
                            malawiRegionDistricts[newStation.region].map(
                              (district) => (
                                <SelectItem key={district} value={district}>
                                  {district}
                                </SelectItem>
                              )
                            )}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Phone Input */}
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone</Label>
                      <Input
                        id="phone"
                        placeholder="(*************"
                        value={newStation.phone}
                        onChange={(e) =>
                          handleNewStationChange("phone", e.target.value)
                        }
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="coordinates">GPS Coordinates</Label>
                      <Input
                        id="coordinates"
                        placeholder="40.7128,-74.0060"
                        value={newStation.coordinates}
                        onChange={(e) =>
                          handleNewStationChange("coordinates", e.target.value)
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="established">Year Established</Label>
                      <Input
                        id="established"
                        placeholder="2024"
                        value={newStation.established}
                        onChange={(e) =>
                          handleNewStationChange("established", e.target.value)
                        }
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Map Location</Label>
                    <p className="text-sm text-muted-foreground">
                      Click on the map to select coordinates
                    </p>
                    <DynamicMap
                      coords={mapCoords}
                      setCoords={setMapCoords}
                      onCoordinatesChange={(coordsString) =>
                        handleNewStationChange("coordinates", coordsString)
                      }
                    />
                  </div>
                  {/* --- Station Manager Section (Optional) --- */}
                  <div className="mt-6 border-t pt-4">
                    <h3 className="font-semibold mb-2">
                      Station Manager (Optional)
                    </h3>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="manager-name">Manager Name</Label>
                        <Input
                          id="manager-name"
                          placeholder="Manager Name"
                          value={newStation.manager_name || ""}
                          onChange={(e) =>
                            handleNewStationChange(
                              "manager_name",
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="manager-email">Manager Email</Label>
                        <Input
                          id="manager-email"
                          placeholder="<EMAIL>"
                          value={newStation.manager_email || ""}
                          onChange={(e) =>
                            handleNewStationChange(
                              "manager_email",
                              e.target.value
                            )
                          }
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="manager-phone">Manager Phone</Label>
                        <Input
                          id="manager-phone"
                          placeholder="(*************"
                          value={newStation.manager_phone || ""}
                          onChange={(e) =>
                            handleNewStationChange(
                              "manager_phone",
                              e.target.value
                            )
                          }
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsAddStationOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button disabled={loading} onClick={handleAddStation}>
                    {!loading ? "Add Station" : "Loading...."}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search stations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedRegion} onValueChange={setSelectedRegion}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Regions</SelectItem>
                {regions.map((region) => (
                  <SelectItem key={region} value={region}>
                    {region}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Station</TableHead>
                <TableHead>District</TableHead>
                <TableHead>Region</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStations.map((station) => (
                <TableRow key={station.id}>
                  <TableCell className="font-medium">{station.name}</TableCell>
                  <TableCell>{station.district}</TableCell>
                  <TableCell>{station.region}</TableCell>
                  <TableCell>{station.phone_number}</TableCell>
                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openGoogleMaps(station.coordinates)}
                      className="flex items-center gap-1"
                    >
                      <MapPin className="w-3 h-3" />
                      GPS
                    </Button>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Link href={`/admin/stations/${station.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4" />
                        </Button>
                      </Link>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
