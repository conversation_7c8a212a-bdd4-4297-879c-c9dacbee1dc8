"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Building2,
  AlertTriangle,
  Truck,
  Activity,
  TrendingUp,
} from "lucide-react";
import { toast } from "sonner";

export default function AdminDashboard() {
  const [stats, setStats] = useState(null);
  const [recentEmergencies, setRecentEmergencies] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem("token");
      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";

      // Fetch stats
      const statsResponse = await fetch(
        `${backendUrl}/api/dashboard/admin/stats`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats([
          {
            title: "Total Users",
            value: statsData.total_users.toString(),
            description: "Active system users",
            icon: Users,
            color: "text-blue-600",
          },
          {
            title: "Fire Stations",
            value: statsData.total_stations.toString(),
            description: "Operational stations",
            icon: Building2,
            color: "text-green-600",
          },
          {
            title: "Active Emergencies",
            value: statsData.active_emergencies.toString(),
            description: "Currently responding",
            icon: AlertTriangle,
            color: "text-red-600",
          },
          {
            title: "Available Vehicles",
            value: statsData.available_vehicles.toString(),
            description: `${statsData.vehicle_availability_percentage}% availability`,
            icon: Truck,
            color: "text-orange-600",
          },
        ]);
      }

      // Fetch recent emergencies
      const emergenciesResponse = await fetch(
        `${backendUrl}/api/dashboard/recent-emergencies`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (emergenciesResponse.ok) {
        const emergenciesData = await emergenciesResponse.json();
        setRecentEmergencies(emergenciesData);
      }
    } catch (error) {
      toast.error("Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        Loading dashboard...
      </div>
    );
  }

  const recentActivities = [
    {
      id: 1,
      type: "emergency",
      message: "New emergency reported at Downtown District",
      time: "2 minutes ago",
      status: "active",
    },
    {
      id: 2,
      type: "user",
      message: "New responder John Smith added to Station 5",
      time: "15 minutes ago",
      status: "completed",
    },
    {
      id: 3,
      type: "vehicle",
      message: "Fire Truck FT-101 marked for maintenance",
      time: "1 hour ago",
      status: "pending",
    },
    {
      id: 4,
      type: "station",
      message: "Station 12 completed monthly equipment check",
      time: "3 hours ago",
      status: "completed",
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Administrator Dashboard</h1>
        <p className="text-muted-foreground">
          Overview of the fire emergency management system
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats &&
          stats.map((stat) => {
            const Icon = stat.icon;
            return (
              <Card key={stat.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {stat.title}
                  </CardTitle>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stat.value}</div>
                  <p className="text-xs text-muted-foreground">
                    {stat.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest system activities and updates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentEmergencies.slice(0, 5).map((emergency) => (
                <div key={emergency.id} className="flex items-start gap-3">
                  <div
                    className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                      emergency.status === "active"
                        ? "bg-red-500"
                        : emergency.status === "resolved"
                        ? "bg-green-500"
                        : "bg-yellow-500"
                    }`}
                  />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">
                      Emergency #{emergency.id} - {emergency.urgency_level}{" "}
                      priority
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {emergency.station_name} •{" "}
                      {new Date(emergency.created_at).toLocaleString()}
                    </p>
                  </div>
                  <Badge
                    variant={
                      emergency.status === "active"
                        ? "destructive"
                        : emergency.status === "resolved"
                        ? "default"
                        : "secondary"
                    }
                    className="text-xs"
                  >
                    {emergency.status}
                  </Badge>
                </div>
              ))}
              {recentEmergencies.length === 0 && (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No recent emergencies
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Status</CardTitle>
            <CardDescription>
              Current system health and performance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">System Uptime</span>
                <Badge variant="default">99.9%</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Response Time</span>
                <Badge variant="default">1.2s</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Active Connections</span>
                <Badge variant="default">156</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Database Status</span>
                <Badge variant="default">Healthy</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
