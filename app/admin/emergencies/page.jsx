"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AlertTriangle, Search, MapPin, Clock, Users, Flame } from "lucide-react"

export default function EmergenciesView() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  const emergencies = [
    {
      id: "EMG-001",
      type: "Structure Fire",
      location: "123 Main St, Downtown",
      coordinates: "40.7128, -74.0060",
      status: "active",
      priority: "high",
      reportedAt: "2024-01-15 14:30",
      assignedStation: "Station 5",
      assignedVehicles: ["FT-101", "FT-102"],
      responders: 8,
      description: "Large structure fire in commercial building",
    },
    {
      id: "EMG-002",
      type: "Vehicle Fire",
      location: "Highway 101, Mile Marker 45",
      coordinates: "40.7589, -73.9851",
      status: "responding",
      priority: "medium",
      reportedAt: "2024-01-15 13:45",
      assignedStation: "Station 3",
      assignedVehicles: ["FT-201"],
      responders: 4,
      description: "Vehicle fire on highway shoulder",
    },
    {
      id: "EMG-003",
      type: "Medical Emergency",
      location: "456 Oak Ave, Residential",
      coordinates: "40.7505, -73.9934",
      status: "resolved",
      priority: "high",
      reportedAt: "2024-01-15 12:15",
      assignedStation: "Station 1",
      assignedVehicles: ["AMB-101"],
      responders: 3,
      description: "Medical emergency - cardiac arrest",
    },
    {
      id: "EMG-004",
      type: "Hazmat Incident",
      location: "Industrial District, Warehouse 7",
      coordinates: "40.7282, -74.0776",
      status: "active",
      priority: "critical",
      reportedAt: "2024-01-15 11:30",
      assignedStation: "Station 2",
      assignedVehicles: ["HAZ-301", "FT-301"],
      responders: 12,
      description: "Chemical spill in industrial facility",
    },
  ]

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "critical":
        return "destructive"
      case "high":
        return "destructive"
      case "medium":
        return "default"
      case "low":
        return "secondary"
      default:
        return "secondary"
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "destructive"
      case "responding":
        return "default"
      case "resolved":
        return "secondary"
      default:
        return "secondary"
    }
  }

  const filteredEmergencies = emergencies.filter((emergency) => {
    const matchesSearch =
      emergency.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emergency.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emergency.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || emergency.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const activeEmergencies = emergencies.filter((e) => e.status === "active").length
  const respondingEmergencies = emergencies.filter((e) => e.status === "responding").length
  const resolvedToday = emergencies.filter((e) => e.status === "resolved").length

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Emergency Management</h1>
        <p className="text-muted-foreground">Monitor and manage all emergency incidents across the system</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Emergencies</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{activeEmergencies}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Responding</CardTitle>
            <Users className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{respondingEmergencies}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Resolved Today</CardTitle>
            <Clock className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{resolvedToday}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Incidents</CardTitle>
            <Flame className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{emergencies.length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Emergency List */}
      <Card>
        <CardHeader>
          <CardTitle>Emergency Incidents</CardTitle>
          <CardDescription>All emergency incidents and their current status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search emergencies..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="responding">Responding</SelectItem>
                <SelectItem value="resolved">Resolved</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Station</TableHead>
                <TableHead>Responders</TableHead>
                <TableHead>Reported</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredEmergencies.map((emergency) => (
                <TableRow key={emergency.id}>
                  <TableCell className="font-medium">{emergency.id}</TableCell>
                  <TableCell>{emergency.type}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm">{emergency.location}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusColor(emergency.status)}>{emergency.status}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getPriorityColor(emergency.priority)}>{emergency.priority}</Badge>
                  </TableCell>
                  <TableCell>{emergency.assignedStation}</TableCell>
                  <TableCell>{emergency.responders}</TableCell>
                  <TableCell>{emergency.reportedAt}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`https://maps.google.com/?q=${emergency.coordinates}`, "_blank")}
                      >
                        <MapPin className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
