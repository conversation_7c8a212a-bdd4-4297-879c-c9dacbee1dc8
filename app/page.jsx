"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import Image from "next/image";
import { Toaster } from "@/components/ui/toaster";

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);

  const BACKEND_URL = process.env.BACKEND_URL;

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const res = await fetch(`${BACKEND_URL}/auth/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });

      const data = await res.json();

      if (!res.ok) {
        console.log(res.status);
        switch (res.status) {
          case 400:
            toast.error(data.detail || "Bad request. Please check your input.");
            break;
          case 401:
            toast.error("Incorrect email or password.");
            break;
          case 403:
            toast.error("You do not have permission to access this resource.");
            break;
          case 404:
            toast.error("Server not found. Try again later.");
            break;
          case 500:
            toast.error("Internal server error. Please try again.");
            break;
          default:
            toast.error(data.detail || "An unexpected error occurred.");
        }
        return;
      }

      // Success
      localStorage.setItem("token", data.access_token);
      localStorage.setItem("user", JSON.stringify(data.user));

      toast.success("Login successful! Redirecting...");

      switch (data.user.role) {
        case "admin":
          window.location.href = "/admin";
          break;
        case "station_manager":
          window.location.href = "/station-manager";
          break;
        case "responder":
          window.location.href = "/responder";
          break;
        case "user":
        default:
          window.location.href = "/dashboard";
          break;
      }
    } catch (err) {
      toast.error("Network error. Please check your connection.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Toaster />
      <Card className="w-full max-w-md shadow-lg rounded-xl">
        <CardHeader className="flex flex-col items-center space-y-4">
          {/* Logo */}
          <Image src="logo.png" alt="Logo" height={100} width={100} />
          <CardTitle className="text-center text-xl font-semibold">
            MyGuardian+ Login
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>

            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Logging in..." : "Login"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
