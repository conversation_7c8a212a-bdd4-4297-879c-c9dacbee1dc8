"use client";
import { useEffect, useRef, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";

export default function WaterRefillPointsViewPage() {
  const mapRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const vectorLayerRef = useRef(null);
  const [refillPoints, setRefillPoints] = useState([]);
  const [loading, setLoading] = useState(true);
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";

  useEffect(() => {
    fetchPoints();
  }, []);

  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    // Create vector source and layer for markers
    const vectorSource = new window.ol.source.Vector();
    const vectorLayer = new window.ol.layer.Vector({
      source: vectorSource,
      style: new window.ol.style.Style({
        image: new window.ol.style.Circle({
          radius: 8,
          fill: new window.ol.style.Fill({ color: "#3b82f6" }),
          stroke: new window.ol.style.Stroke({ color: "#ffffff", width: 2 }),
        }),
      }),
    });

    // Create simple map with OSM tiles and vector layer
    const mapEl = new window.ol.Map({
      target: mapRef.current,
      layers: [
        new window.ol.layer.Tile({
          source: new window.ol.source.OSM(),
        }),
        vectorLayer,
      ],
      view: new window.ol.View({
        center: window.ol.proj.fromLonLat([34.0194, -11.4596]),
        zoom: 7,
      }),
    });

    // Optional: Add popup on marker click to show point details
    mapEl.on("click", (evt) => {
      const feature = mapEl.forEachFeatureAtPixel(
        evt.pixel,
        (feature) => feature
      );
      if (feature) {
        const name = feature.get("name");
        const description = feature.get("description");
        if (name) {
          // You could add a popup here or show details in some other way
          console.log(`Clicked on: ${name} - ${description}`);
        }
      }
    });

    mapInstanceRef.current = mapEl;
    vectorLayerRef.current = vectorLayer;

    // Cleanup function
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.setTarget(null);
        mapInstanceRef.current = null;
      }
      vectorLayerRef.current = null;
    };
  }, []);

  // Effect to update markers when refill points change
  useEffect(() => {
    if (!vectorLayerRef.current || !refillPoints.length) return;

    const vectorSource = vectorLayerRef.current.getSource();

    // Clear existing markers
    vectorSource.clear();

    // Add markers for all refill points
    refillPoints.forEach((point) => {
      const marker = new window.ol.Feature({
        geometry: new window.ol.geom.Point(
          window.ol.proj.fromLonLat([point.longitude, point.latitude])
        ),
        name: point.name,
        description: point.description,
        id: point.id,
      });

      vectorSource.addFeature(marker);
    });

    // Auto-fit map to show all markers if there are points
    if (refillPoints.length > 0) {
      const extent = vectorSource.getExtent();
      if (extent && extent.every((coord) => isFinite(coord))) {
        mapInstanceRef.current.getView().fit(extent, {
          padding: [50, 50, 50, 50],
          maxZoom: 15,
        });
      }
    }
  }, [refillPoints]);

  const fetchPoints = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("token");
      const res = await fetch(`${backendUrl}/water-refill/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (res.ok) {
        const points = await res.json();
        setRefillPoints(points);
      } else {
        console.error("Failed to fetch refill points");
      }
    } catch (error) {
      console.error("Error fetching points:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Water Refill Points Map</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Map Container */}
          <div className="w-full">
            <div
              ref={mapRef}
              className="w-full border rounded-md"
              style={{ height: "450px" }}
            />
            <p className="text-sm text-gray-600 mt-2">
              {refillPoints.length > 0
                ? `Showing ${refillPoints.length} water refill point${
                    refillPoints.length !== 1 ? "s" : ""
                  }`
                : "Loading refill points..."}
            </p>
          </div>

          {/* Points Table */}
          <div className="w-full">
            <h3 className="text-lg font-semibold mb-3">
              Available Refill Points
            </h3>
            {loading ? (
              <div className="text-center py-8 text-gray-500 border rounded-md">
                <p>Loading refill points...</p>
              </div>
            ) : refillPoints.length > 0 ? (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Latitude</TableHead>
                      <TableHead>Longitude</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {refillPoints.map((point) => (
                      <TableRow key={point.id}>
                        <TableCell className="font-medium">
                          {point.name}
                        </TableCell>
                        <TableCell>
                          {point.description || "No description"}
                        </TableCell>
                        <TableCell>{point.latitude}</TableCell>
                        <TableCell>{point.longitude}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 border rounded-md">
                <p>No refill points available at the moment.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
