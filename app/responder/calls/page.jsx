"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON><PERSON>gle, MapPin, Clock, Users, Radio, CheckCircle } from "lucide-react"

export default function ActiveCalls() {
  const activeCalls = [
    {
      id: "ALT-001",
      type: "Structure Fire",
      location: "123 Oak Street, Downtown",
      coordinates: "40.7128,-74.0060",
      priority: "critical",
      status: "responding",
      assignedAt: "2024-01-15 14:30",
      estimatedArrival: "2 minutes",
      assignedVehicle: "Engine 5-1",
      teamMembers: ["<PERSON>", "<PERSON> Davis", "<PERSON><PERSON> <PERSON>"],
      description: "Large structure fire in 3-story commercial building. Multiple units requested.",
      instructions:
        "Approach from north side. Water supply available on Oak Street. Avoid south entrance due to structural concerns.",
    },
  ]

  const availableCalls = [
    {
      id: "ALT-002",
      type: "Medical Emergency",
      location: "456 Pine Avenue, Residential",
      coordinates: "40.7589,-73.9851",
      priority: "high",
      status: "pending",
      reportedAt: "2024-01-15 14:35",
      estimatedResponse: "5 minutes",
      description: "Cardiac arrest - 65-year-old male, CPR in progress.",
      requiredVehicles: ["Ambulance"],
    },
    {
      id: "ALT-003",
      type: "Vehicle Accident",
      location: "Highway 101 & Main Street",
      coordinates: "40.7505,-73.9934",
      priority: "medium",
      status: "pending",
      reportedAt: "2024-01-15 14:40",
      estimatedResponse: "8 minutes",
      description: "Multi-vehicle accident with possible injuries. Traffic blocked.",
      requiredVehicles: ["Engine", "Ambulance"],
    },
  ]

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "critical":
        return "destructive"
      case "high":
        return "destructive"
      case "medium":
        return "default"
      case "low":
        return "secondary"
      default:
        return "secondary"
    }
  }

  const openGoogleMaps = (coordinates) => {
    const [lat, lng] = coordinates.split(",")
    window.open(`https://maps.google.com/?q=${lat},${lng}`, "_blank")
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Active Calls</h1>
        <p className="text-muted-foreground">Current emergency assignments and available calls</p>
      </div>

      {/* Current Assignment */}
      {activeCalls.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Your Current Assignment</h2>
          {activeCalls.map((call) => (
            <Card key={call.id} className="border-red-200 bg-red-50">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-800">
                  <AlertTriangle className="h-5 w-5" />
                  {call.id} - {call.type}
                </CardTitle>
                <CardDescription className="text-red-600">
                  Priority: <Badge variant={getPriorityColor(call.priority)}>{call.priority}</Badge>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground">Location</h4>
                      <p className="text-sm">{call.location}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground">ETA</h4>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span className="text-sm font-medium">{call.estimatedArrival}</span>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground">Vehicle</h4>
                      <p className="text-sm font-medium">{call.assignedVehicle}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground">Status</h4>
                      <Badge variant="destructive">{call.status}</Badge>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-2">Description</h4>
                    <p className="text-sm bg-white p-3 rounded-lg border">{call.description}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-2">Special Instructions</h4>
                    <p className="text-sm bg-yellow-50 text-yellow-800 p-3 rounded-lg border border-yellow-200">
                      {call.instructions}
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-2">Team Members</h4>
                    <div className="flex gap-2">
                      {call.teamMembers.map((member) => (
                        <Badge key={member} variant="outline">
                          <Users className="w-3 h-3 mr-1" />
                          {member}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div className="flex gap-4">
                    <Button onClick={() => openGoogleMaps(call.coordinates)} className="flex items-center gap-2">
                      <MapPin className="w-4 h-4" />
                      Navigate to Location
                    </Button>
                    <Button variant="outline" className="flex items-center gap-2 bg-transparent">
                      <Radio className="w-4 h-4" />
                      Radio Command
                    </Button>
                    <Button variant="outline" className="flex items-center gap-2 bg-transparent">
                      <CheckCircle className="w-4 h-4" />
                      Update Status
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Available Calls */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Available Emergency Calls</h2>
        <div className="grid gap-4">
          {availableCalls.map((call) => (
            <Card key={call.id}>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5" />
                    {call.id} - {call.type}
                  </div>
                  <Badge variant={getPriorityColor(call.priority)}>{call.priority}</Badge>
                </CardTitle>
                <CardDescription>{call.location}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground">Reported</h4>
                      <p className="text-sm">{call.reportedAt}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground">Response Time</h4>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span className="text-sm">{call.estimatedResponse}</span>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground">Required Vehicles</h4>
                      <div className="flex gap-1">
                        {call.requiredVehicles.map((vehicle) => (
                          <Badge key={vehicle} variant="outline" className="text-xs">
                            {vehicle}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-2">Description</h4>
                    <p className="text-sm bg-muted p-3 rounded-lg">{call.description}</p>
                  </div>
                  <div className="flex gap-4">
                    <Button onClick={() => openGoogleMaps(call.coordinates)} variant="outline" size="sm">
                      <MapPin className="w-4 h-4 mr-2" />
                      View Location
                    </Button>
                    <Button variant="outline" size="sm" disabled>
                      Request Assignment
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Communication Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Radio className="h-5 w-5" />
            Communication
          </CardTitle>
          <CardDescription>Quick communication options</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button className="justify-start bg-transparent" variant="outline">
              <Radio className="w-4 h-4 mr-2" />
              Contact Dispatch
            </Button>
            <Button className="justify-start bg-transparent" variant="outline">
              <Users className="w-4 h-4 mr-2" />
              Team Radio
            </Button>
            <Button className="justify-start bg-transparent" variant="outline">
              <AlertTriangle className="w-4 h-4 mr-2" />
              Emergency Backup
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
