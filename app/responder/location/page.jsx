"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MapPin, Navigation, Radio, Clock, Truck } from "lucide-react"

export default function ResponderLocation() {
  const [currentLocation, setCurrentLocation] = useState({
    latitude: 40.7128,
    longitude: -74.006,
    accuracy: "5 meters",
    lastUpdated: "2024-01-15 14:45:30",
  })

  const vehicleInfo = {
    name: "Engine 5-1",
    type: "Fire Engine",
    status: "responding",
    destination: "123 Oak Street, Downtown",
    eta: "2 minutes",
  }

  const nearbyUnits = [
    {
      id: "engine-5-2",
      name: "Engine 5-2",
      type: "Fire Engine",
      distance: "0.8 miles",
      status: "available",
      location: "Station 5",
    },
    {
      id: "ambulance-5-1",
      name: "Ambulance 5-1",
      type: "Ambulance",
      distance: "1.2 miles",
      status: "available",
      location: "Station 5",
    },
    {
      id: "ladder-3-1",
      name: "Ladder 3-1",
      type: "Ladder Truck",
      distance: "2.1 miles",
      status: "responding",
      location: "En route to Pine Ave",
    },
  ]

  const updateLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setCurrentLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: `${Math.round(position.coords.accuracy)} meters`,
            lastUpdated: new Date().toLocaleString(),
          })
        },
        (error) => {
          console.error("Error getting location:", error)
        },
      )
    }
  }

  const openGoogleMaps = () => {
    window.open(`https://maps.google.com/?q=${currentLocation.latitude},${currentLocation.longitude}`, "_blank")
  }

  const getStatusColor = (status) => {
    switch (status) {
      case "available":
        return "default"
      case "responding":
        return "destructive"
      case "maintenance":
        return "secondary"
      default:
        return "secondary"
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Location & Navigation</h1>
        <p className="text-muted-foreground">Track your location and coordinate with nearby units</p>
      </div>

      {/* Current Location */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Current Location
          </CardTitle>
          <CardDescription>Your real-time GPS location and status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Coordinates</h4>
                <p className="text-sm font-mono">
                  {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}
                </p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Accuracy</h4>
                <p className="text-sm">{currentLocation.accuracy}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Last Updated</h4>
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  <span className="text-sm">{currentLocation.lastUpdated}</span>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Status</h4>
                <Badge variant="default">Location Tracking Active</Badge>
              </div>
            </div>
            <div className="flex gap-4">
              <Button onClick={updateLocation} className="flex items-center gap-2">
                <Navigation className="w-4 h-4" />
                Update Location
              </Button>
              <Button onClick={openGoogleMaps} variant="outline" className="flex items-center gap-2 bg-transparent">
                <MapPin className="w-4 h-4" />
                View on Map
              </Button>
              <Button variant="outline" className="flex items-center gap-2 bg-transparent">
                <Radio className="w-4 h-4" />
                Share Location
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Vehicle Status
          </CardTitle>
          <CardDescription>Current vehicle assignment and destination</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Assigned Vehicle</h4>
                <p className="font-medium">{vehicleInfo.name}</p>
                <p className="text-sm text-muted-foreground">{vehicleInfo.type}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Status</h4>
                <Badge variant={getStatusColor(vehicleInfo.status)}>{vehicleInfo.status}</Badge>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">Destination</h4>
                <p className="text-sm">{vehicleInfo.destination}</p>
              </div>
              <div>
                <h4 className="font-medium text-sm text-muted-foreground">ETA</h4>
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  <span className="text-sm font-medium">{vehicleInfo.eta}</span>
                </div>
              </div>
            </div>
            <Button
              onClick={() =>
                window.open(`https://maps.google.com/?q=${encodeURIComponent(vehicleInfo.destination)}`, "_blank")
              }
              className="flex items-center gap-2"
            >
              <Navigation className="w-4 h-4" />
              Navigate to Destination
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Nearby Units */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Radio className="h-5 w-5" />
            Nearby Units
          </CardTitle>
          <CardDescription>Other emergency vehicles in your area</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {nearbyUnits.map((unit) => (
              <div key={unit.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium">{unit.name}</span>
                    <Badge variant={getStatusColor(unit.status)}>{unit.status}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">{unit.type}</p>
                  <p className="text-xs text-muted-foreground">{unit.location}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{unit.distance}</p>
                  <Button variant="outline" size="sm" className="mt-2 bg-transparent">
                    <Radio className="w-3 h-3 mr-1" />
                    Contact
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Emergency Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Emergency Actions</CardTitle>
          <CardDescription>Quick actions for emergency situations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" className="justify-start bg-red-50 border-red-200 text-red-700">
              <Radio className="w-4 h-4 mr-2" />
              Emergency Backup Request
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <MapPin className="w-4 h-4 mr-2" />
              Mark Hazard Location
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <Navigation className="w-4 h-4 mr-2" />
              Request Route Guidance
            </Button>
            <Button variant="outline" className="justify-start bg-transparent">
              <Clock className="w-4 h-4 mr-2" />
              Update ETA
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
