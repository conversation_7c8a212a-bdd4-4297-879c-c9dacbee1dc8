"use client";

import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { ResponderSidebar } from "@/components/responder/responder-sidebar";
import { AuthWrapper } from "@/components/auth-wrapper";
import { Toaster } from "@/components/ui/sonner";

export default function ResponderLayout({ children }) {
  return (
    <AuthWrapper>
      <SidebarProvider>
        <div className="flex min-h-screen w-full">
          <ResponderSidebar />
          <div className="flex-1 flex flex-col">
            <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
              <div className="flex h-16 items-center px-4 gap-4">
                <SidebarTrigger />
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 flex items-center justify-center">
                    <img src="/logo.png" alt="Logo" className="w-10 h-10" />
                  </div>
                  <div>
                    <h1 className="text-lg font-semibold">
                      MyGuardianPlus Fire Response
                    </h1>
                    <p className="text-sm text-muted-foreground">
                      Emergency Responder Dashboard
                    </p>
                  </div>
                </div>
              </div>
            </header>
            <main className="flex-1 p-6">{children}</main>
          </div>
        </div>
        <Toaster />
      </SidebarProvider>
    </AuthWrapper>
  );
}
