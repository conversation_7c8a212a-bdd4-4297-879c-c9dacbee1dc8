"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  AlertTriangle,
  MapPin,
  Clock,
  Users,
  Truck,
  CheckCircle,
  Radio,
  FileText,
} from "lucide-react";
import { toast } from "sonner";
import { FireResponseReportModal } from "@/components/responder/fire-response-report-modal";

export default function ResponderDashboard() {
  const [responderInfo, setResponderInfo] = useState(null);
  const [stationInfo, setStationInfo] = useState(null);
  const [stats, setStats] = useState(null);
  const [assignedEmergencies, setAssignedEmergencies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isReportModalOpen, setIsReportModalOpen] = useState(false);
  const [selectedEmergencyForReport, setSelectedEmergencyForReport] =
    useState(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem("token");
      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";

      // Fetch user profile
      const userResponse = await fetch(`${backendUrl}/api/profile/me`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (userResponse.ok) {
        const userData = await userResponse.json();
        setResponderInfo(userData);
      }

      // Fetch station info
      const stationResponse = await fetch(
        `${backendUrl}/api/profile/station-info`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (stationResponse.ok) {
        const stationData = await stationResponse.json();
        setStationInfo(stationData.station);
      }

      // Fetch stats
      const statsResponse = await fetch(
        `${backendUrl}/api/dashboard/responder/stats`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }

      // Fetch assigned emergencies
      const emergenciesResponse = await fetch(
        `${backendUrl}/api/dashboard/responder/assigned-emergencies`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (emergenciesResponse.ok) {
        const emergenciesData = await emergenciesResponse.json();
        setAssignedEmergencies(emergenciesData.assigned_emergencies || []);
      }
    } catch (error) {
      toast.error("Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        Loading dashboard...
      </div>
    );
  }

  // Use real assigned emergencies data
  const activeAssignments = assignedEmergencies;

  const recentCalls = [
    {
      id: "ALT-098",
      type: "Medical Emergency",
      location: "456 Pine Avenue",
      completedAt: "2024-01-15 12:45",
      duration: "1h 15m",
      outcome: "Patient transported successfully",
    },
    {
      id: "ALT-097",
      type: "Vehicle Accident",
      location: "Highway 101",
      completedAt: "2024-01-15 09:30",
      duration: "45m",
      outcome: "Scene cleared, no injuries",
    },
    {
      id: "ALT-096",
      type: "Structure Fire",
      location: "789 Elm Street",
      completedAt: "2024-01-14 16:20",
      duration: "2h 30m",
      outcome: "Fire extinguished, building saved",
    },
  ];

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "critical":
        return "destructive";
      case "high":
        return "destructive";
      case "medium":
        return "default";
      case "low":
        return "secondary";
      default:
        return "secondary";
    }
  };

  const openGoogleMaps = (coordinates) => {
    const [lat, lng] = coordinates.split(",");
    window.open(`https://maps.google.com/?q=${lat},${lng}`, "_blank");
  };

  const handleResolveEmergency = (emergency) => {
    setSelectedEmergencyForReport(emergency);
    setIsReportModalOpen(true);
  };

  const handleReportSuccess = () => {
    // Refresh dashboard data
    fetchDashboardData();
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Responder Dashboard</h1>
        <p className="text-muted-foreground">
          {responderInfo
            ? `${responderInfo.full_name} • ${
                stationInfo?.name || "No Station"
              }`
            : "Loading..."}
        </p>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Current Status
            </CardTitle>
            <Radio className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats?.status || "On Duty"}
            </div>
            <p className="text-xs text-muted-foreground">Current status</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assignment</CardTitle>
            <Truck className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.current_vehicle?.vehicle_number || "None"}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats?.current_vehicle?.vehicle_type || "No vehicle assigned"}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Calls</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {stats?.active_assignments || 0}
            </div>
            <p className="text-xs text-muted-foreground">Assigned to you</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Calls Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.completed_calls_today || 0}
            </div>
            <p className="text-xs text-muted-foreground">Calls resolved</p>
          </CardContent>
        </Card>
      </div>

      {/* Active Assignment */}
      {activeAssignments.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="h-5 w-5" />
              Active Emergency Assignment
            </CardTitle>
            <CardDescription className="text-red-600">
              You are currently responding to an emergency
            </CardDescription>
          </CardHeader>
          <CardContent>
            {activeAssignments.map((assignment) => (
              <div key={assignment.id} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">
                      Emergency ID
                    </h4>
                    <p className="font-medium text-red-800">#{assignment.id}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">
                      Priority
                    </h4>
                    <Badge variant={getPriorityColor(assignment.urgency_level)}>
                      {assignment.urgency_level}
                    </Badge>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">
                      Location
                    </h4>
                    <p className="text-sm">
                      {assignment.latitude}, {assignment.longitude}
                    </p>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground">
                      Role
                    </h4>
                    <Badge variant="outline">
                      {assignment.role === "driver" ? "Driver" : "Team Member"}
                    </Badge>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground mb-2">
                    Assignment Details
                  </h4>
                  <div className="flex gap-2">
                    {assignment.assigned_team && (
                      <Badge variant="outline">
                        Team: {assignment.assigned_team}
                      </Badge>
                    )}
                    {assignment.assigned_vehicle && (
                      <Badge variant="outline">
                        Vehicle: {assignment.assigned_vehicle}
                      </Badge>
                    )}
                    {assignment.assigned_driver && (
                      <Badge variant="outline">
                        Driver: {assignment.assigned_driver}
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="flex gap-4">
                  <Button
                    onClick={() =>
                      openGoogleMaps(
                        `${assignment.latitude},${assignment.longitude}`
                      )
                    }
                    className="flex items-center gap-2"
                  >
                    <MapPin className="w-4 h-4" />
                    Navigate to Location
                  </Button>
                  <Button
                    variant="outline"
                    className="flex items-center gap-2 bg-transparent"
                  >
                    <Radio className="w-4 h-4" />
                    Radio Command
                  </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Recent Calls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Active Emergencies
          </CardTitle>
          <CardDescription>
            Emergencies requiring resolution reports
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {activeAssignments.map((emergency) => (
              <div
                key={emergency.id}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium">
                      Emergency #{emergency.id}
                    </span>
                    <Badge variant={getPriorityColor(emergency.urgency_level)}>
                      {emergency.urgency_level}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-1">
                    {emergency.latitude}, {emergency.longitude}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Status: {emergency.status}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    onClick={() =>
                      openGoogleMaps(
                        `${emergency.latitude},${emergency.longitude}`
                      )
                    }
                    variant="outline"
                    size="sm"
                  >
                    <MapPin className="w-4 h-4" />
                  </Button>
                  <Button
                    onClick={() => handleResolveEmergency(emergency)}
                    variant="default"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <FileText className="w-4 h-4" />
                    Resolve
                  </Button>
                </div>
              </div>
            ))}
            {activeAssignments.length === 0 && (
              <p className="text-sm text-muted-foreground text-center py-4">
                No active emergencies assigned
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common responder tasks and communications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button className="justify-start bg-transparent" variant="outline">
              <Radio className="w-4 h-4 mr-2" />
              Contact Dispatch
            </Button>
            <Button className="justify-start bg-transparent" variant="outline">
              <Users className="w-4 h-4 mr-2" />
              Team Communication
            </Button>
            <Button className="justify-start bg-transparent" variant="outline">
              <MapPin className="w-4 h-4 mr-2" />
              Update Location
            </Button>
            <Button className="justify-start bg-transparent" variant="outline">
              <CheckCircle className="w-4 h-4 mr-2" />
              Report Status
            </Button>
          </div>
        </CardContent>
      </Card>

      <FireResponseReportModal
        isOpen={isReportModalOpen}
        onClose={() => setIsReportModalOpen(false)}
        emergency={selectedEmergencyForReport}
        onSuccess={handleReportSuccess}
      />
    </div>
  );
}
