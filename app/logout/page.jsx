"use client";

import { useEffect } from "react";
import { motion } from "framer-motion";
import { LogOut } from "lucide-react";

export default function LogoutPage() {
  useEffect(() => {
    const timer = setTimeout(() => {
      // Clear stored auth info
      localStorage.removeItem("token");
      localStorage.removeItem("user");

      // Redirect to login
      window.location.href = "/";
    }, 2000); // 2 second animation before redirect

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50">
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1.2, opacity: 1 }}
        transition={{ duration: 0.6, repeat: Infinity, repeatType: "reverse" }}
        className="p-2 rounded-full bg-red-500 flex items-center justify-center"
      >
        <LogOut className="text-white" />
      </motion.div>
      <p className="mt-6 text-gray-600 text-lg font-medium">
        Logging you out...
      </p>
    </div>
  );
}
