"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { History, Search, Eye, MapPin, Clock, Users, Calendar } from "lucide-react"

export default function AlertHistory() {
  const [searchTerm, setSearchTerm] = useState("")
  const [typeFilter, setTypeFilter] = useState("all")
  const [priorityFilter, setPriorityFilter] = useState("all")
  const [selected<PERSON><PERSON><PERSON>, setSelectedAlert] = useState(null)

  const historicalAlerts = [
    {
      id: "ALT-098",
      type: "Structure Fire",
      location: "789 Elm Street, Commercial District",
      coordinates: "40.7282,-74.0776",
      priority: "critical",
      status: "resolved",
      reportedAt: "2024-01-14 16:45",
      resolvedAt: "2024-01-14 19:30",
      responseTime: "3.5 minutes",
      assignedVehicles: ["Engine 5-1", "Engine 5-2", "Ladder 5-1"],
      responders: 12,
      duration: "2h 45m",
      outcome: "Fire extinguished, no casualties",
      reportedBy: "911 Dispatch",
      description: "Large warehouse fire with potential hazmat concerns. Multiple units responded.",
    },
    {
      id: "ALT-097",
      type: "Medical Emergency",
      location: "321 Maple Avenue, Residential",
      coordinates: "40.7614,-73.9776",
      priority: "high",
      status: "resolved",
      reportedAt: "2024-01-14 14:20",
      resolvedAt: "2024-01-14 15:45",
      responseTime: "2.8 minutes",
      assignedVehicles: ["Ambulance 5-1"],
      responders: 2,
      duration: "1h 25m",
      outcome: "Patient transported to hospital, stable condition",
      reportedBy: "Family Member",
      description: "Elderly patient with chest pains and difficulty breathing.",
    },
    {
      id: "ALT-096",
      type: "Vehicle Accident",
      location: "Highway 101, Mile Marker 23",
      coordinates: "40.7505,-73.9934",
      priority: "medium",
      status: "resolved",
      reportedAt: "2024-01-14 11:15",
      resolvedAt: "2024-01-14 13:00",
      responseTime: "4.2 minutes",
      assignedVehicles: ["Engine 5-2", "Ambulance 5-2", "Rescue 5-1"],
      responders: 8,
      duration: "1h 45m",
      outcome: "Scene cleared, 2 patients transported with minor injuries",
      reportedBy: "State Police",
      description: "Multi-vehicle collision with fuel spill and traffic obstruction.",
    },
    {
      id: "ALT-095",
      type: "Hazmat Incident",
      location: "Industrial Park, Building 7",
      coordinates: "40.7128,-74.0060",
      priority: "critical",
      status: "resolved",
      reportedAt: "2024-01-13 09:30",
      resolvedAt: "2024-01-13 14:15",
      responseTime: "5.1 minutes",
      assignedVehicles: ["Hazmat 5-1", "Engine 5-1", "Ambulance 5-1"],
      responders: 10,
      duration: "4h 45m",
      outcome: "Chemical spill contained, area decontaminated",
      reportedBy: "Plant Manager",
      description: "Chemical leak in manufacturing facility requiring hazmat response.",
    },
    {
      id: "ALT-094",
      type: "Rescue Operation",
      location: "Riverside Park, Near Bridge",
      coordinates: "40.7589,-73.9851",
      priority: "high",
      status: "resolved",
      reportedAt: "2024-01-13 07:45",
      resolvedAt: "2024-01-13 09:20",
      responseTime: "3.8 minutes",
      assignedVehicles: ["Rescue 5-1", "Ambulance 5-1"],
      responders: 6,
      duration: "1h 35m",
      outcome: "Person rescued from water, transported to hospital",
      reportedBy: "Park Visitor",
      description: "Water rescue operation for person who fell into river.",
    },
    {
      id: "ALT-093",
      type: "Structure Fire",
      location: "456 Oak Street, Residential",
      coordinates: "40.7282,-74.0776",
      priority: "high",
      status: "resolved",
      reportedAt: "2024-01-12 22:15",
      resolvedAt: "2024-01-13 01:30",
      responseTime: "2.9 minutes",
      assignedVehicles: ["Engine 5-1", "Ladder 5-1", "Ambulance 5-2"],
      responders: 9,
      duration: "3h 15m",
      outcome: "Fire extinguished, family evacuated safely",
      reportedBy: "Neighbor",
      description: "House fire in residential area, family trapped on second floor.",
    },
  ]

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "critical":
        return "destructive"
      case "high":
        return "destructive"
      case "medium":
        return "default"
      case "low":
        return "secondary"
      default:
        return "secondary"
    }
  }

  const filteredAlerts = historicalAlerts.filter((alert) => {
    const matchesSearch =
      alert.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      alert.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      alert.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = typeFilter === "all" || alert.type === typeFilter
    const matchesPriority = priorityFilter === "all" || alert.priority === priorityFilter
    return matchesSearch && matchesType && matchesPriority
  })

  const openGoogleMaps = (coordinates) => {
    const [lat, lng] = coordinates.split(",")
    window.open(`https://maps.google.com/?q=${lat},${lng}`, "_blank")
  }

  const totalResolved = historicalAlerts.length
  const avgResponseTime = "3.4 minutes"
  const totalResponders = historicalAlerts.reduce((sum, alert) => sum + alert.responders, 0)
  const criticalIncidents = historicalAlerts.filter((a) => a.priority === "critical").length

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Alert History</h1>
        <p className="text-muted-foreground">Review past emergency responses and performance metrics</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Resolved</CardTitle>
            <History className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalResolved}</div>
            <p className="text-xs text-muted-foreground">Last 30 days</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <Clock className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{avgResponseTime}</div>
            <p className="text-xs text-muted-foreground">Target: Under 5 min</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Responders</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalResponders}</div>
            <p className="text-xs text-muted-foreground">Personnel deployed</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Incidents</CardTitle>
            <Badge className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{criticalIncidents}</div>
            <p className="text-xs text-muted-foreground">High priority calls</p>
          </CardContent>
        </Card>
      </div>

      {/* Alert History Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Emergency Response History
          </CardTitle>
          <CardDescription>Complete record of resolved emergency incidents</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search alerts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="Structure Fire">Structure Fire</SelectItem>
                <SelectItem value="Medical Emergency">Medical Emergency</SelectItem>
                <SelectItem value="Vehicle Accident">Vehicle Accident</SelectItem>
                <SelectItem value="Hazmat Incident">Hazmat Incident</SelectItem>
                <SelectItem value="Rescue Operation">Rescue Operation</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Alert ID</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Response Time</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Responders</TableHead>
                <TableHead>Resolved</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAlerts.map((alert) => (
                <TableRow key={alert.id}>
                  <TableCell className="font-medium">{alert.id}</TableCell>
                  <TableCell>{alert.type}</TableCell>
                  <TableCell className="max-w-xs">
                    <div className="truncate" title={alert.location}>
                      {alert.location}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getPriorityColor(alert.priority)}>{alert.priority}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {alert.responseTime}
                    </div>
                  </TableCell>
                  <TableCell>{alert.duration}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Users className="w-3 h-3" />
                      {alert.responders}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1 text-sm">
                      <Calendar className="w-3 h-3" />
                      {new Date(alert.resolvedAt).toLocaleDateString()}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm" onClick={() => setSelectedAlert(alert)}>
                            <Eye className="w-4 h-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle className="flex items-center gap-2">
                              <History className="w-5 h-5" />
                              {selectedAlert?.id} - {selectedAlert?.type}
                            </DialogTitle>
                            <DialogDescription>Detailed incident report and response summary</DialogDescription>
                          </DialogHeader>
                          {selectedAlert && (
                            <div className="space-y-6">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <h4 className="font-medium text-sm text-muted-foreground">Location</h4>
                                  <p className="text-sm">{selectedAlert.location}</p>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm text-muted-foreground">Priority</h4>
                                  <Badge variant={getPriorityColor(selectedAlert.priority)}>
                                    {selectedAlert.priority}
                                  </Badge>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm text-muted-foreground">Reported At</h4>
                                  <p className="text-sm">{selectedAlert.reportedAt}</p>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm text-muted-foreground">Resolved At</h4>
                                  <p className="text-sm">{selectedAlert.resolvedAt}</p>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm text-muted-foreground">Response Time</h4>
                                  <p className="text-sm">{selectedAlert.responseTime}</p>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm text-muted-foreground">Total Duration</h4>
                                  <p className="text-sm">{selectedAlert.duration}</p>
                                </div>
                              </div>
                              <div>
                                <h4 className="font-medium text-sm text-muted-foreground mb-2">Description</h4>
                                <p className="text-sm bg-muted p-3 rounded-lg">{selectedAlert.description}</p>
                              </div>
                              <div>
                                <h4 className="font-medium text-sm text-muted-foreground mb-2">Assigned Vehicles</h4>
                                <div className="flex gap-2">
                                  {selectedAlert.assignedVehicles.map((vehicle) => (
                                    <Badge key={vehicle} variant="outline">
                                      {vehicle}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                              <div>
                                <h4 className="font-medium text-sm text-muted-foreground mb-2">Outcome</h4>
                                <p className="text-sm bg-green-50 text-green-800 p-3 rounded-lg">
                                  {selectedAlert.outcome}
                                </p>
                              </div>
                              <div className="flex gap-4">
                                <Button
                                  variant="outline"
                                  onClick={() => openGoogleMaps(selectedAlert.coordinates)}
                                  className="flex items-center gap-2 bg-transparent"
                                >
                                  <MapPin className="w-4 h-4" />
                                  View Location
                                </Button>
                              </div>
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openGoogleMaps(alert.coordinates)}
                        title="View location on map"
                      >
                        <MapPin className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
