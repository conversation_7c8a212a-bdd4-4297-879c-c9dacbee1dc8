"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertTriangle,
  MapPin,
  Clock,
  Users,
  Truck,
  CheckCircle,
  Eye,
} from "lucide-react";
import { toast } from "sonner";

export default function ActiveAlerts() {
  const [selectedAlert, setSelectedAlert] = useState(null);
  const [isDispatchOpen, setIsDispatchOpen] = useState(false);
  const [alerts, setAlerts] = useState([]);
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchAlerts() {
      setLoading(true);
      try {
        const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
        const token =
          typeof window !== "undefined" ? localStorage.getItem("token") : null;
        const res = await fetch(`${backendUrl}/station/emergencies`, {
          headers: {
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
        });
        if (!res.ok) throw new Error("Failed to fetch alerts");
        const data = await res.json();
        setAlerts(data);
      } catch (e) {
        setAlerts([]);
      } finally {
        setLoading(false);
      }
    }
    async function fetchVehicles() {
      try {
        const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
        const token =
          typeof window !== "undefined" ? localStorage.getItem("token") : null;
        const res = await fetch(`${backendUrl}/station/vehicles`, {
          headers: {
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
        });
        if (!res.ok) throw new Error("Failed to fetch vehicles");
        const data = await res.json();
        setVehicles(data);
      } catch {
        setVehicles([]);
      }
    }
    fetchAlerts();
    fetchVehicles();
  }, []);

  const availableVehicles = vehicles.filter((v) => v.status === "available");

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "destructive";
      case "medium":
        return "default";
      case "low":
        return "secondary";
      default:
        return "secondary";
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "destructive";
      case "dispatched":
        return "default";
      case "resolved":
        return "secondary";
      default:
        return "secondary";
    }
  };

  // Assign vehicle(s) to alert and update vehicle status
  const handleDispatchVehicle = async (alertId, vehicleId) => {
    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
      const token =
        typeof window !== "undefined" ? localStorage.getItem("token") : null;
      // Use correct endpoint: /station/dispatch (POST)
      const res = await fetch(`${backendUrl}/station/dispatch`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        body: JSON.stringify({
          emergency_id: alertId,
          vehicle_ids: [vehicleId],
        }),
      });
      if (!res.ok) throw new Error("Failed to dispatch vehicle");
      setVehicles((prev) =>
        prev.map((v) =>
          v.id === vehicleId ? { ...v, status: "dispatched" } : v
        )
      );
      toast.success("Vehicle dispatched to emergency");
      setAlerts((prev) =>
        prev.map((a) => (a.id === alertId ? { ...a, status: "dispatched" } : a))
      );
    } catch {
      toast.error("Failed to dispatch vehicle");
    }
    setIsDispatchOpen(false);
  };

  // Mark alert as resolved (use /station/resolve/{emergency_id} POST)
  const handleResolveAlert = async (alertId) => {
    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
      const token =
        typeof window !== "undefined" ? localStorage.getItem("token") : null;
      const res = await fetch(`${backendUrl}/station/resolve/${alertId}`, {
        method: "POST",
        headers: {
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
      });
      if (!res.ok) throw new Error("Failed to resolve alert");
      setAlerts((prev) =>
        prev.map((a) => (a.id === alertId ? { ...a, status: "resolved" } : a))
      );
      toast.success("Alert marked as resolved");
    } catch {
      toast.error("Failed to resolve alert");
    }
  };

  const openGoogleMaps = (alert) => {
    if (alert.latitude && alert.longitude) {
      window.open(
        `https://maps.google.com/?q=${alert.latitude},${alert.longitude}`,
        "_blank"
      );
    } else {
      toast.error("No location data available");
    }
  };

  const handleAutoAssign = async (alertId) => {
    try {
      const token = localStorage.getItem("token");
      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";

      const response = await fetch(
        `${backendUrl}/api/station-manager/auto-assign-alert/${alertId}`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response.ok) {
        const result = await response.json();
        toast.success(
          `Alert assigned successfully! Team: ${result.assigned_team}, Driver: ${result.assigned_driver}, Vehicle: ${result.assigned_vehicle}`
        );
        fetchAlerts(); // Refresh the alerts list
      } else {
        const error = await response.json();
        toast.error(error.detail || "Failed to auto-assign resources");
      }
    } catch (error) {
      console.error("Error auto-assigning alert:", error);
      toast.error("Failed to auto-assign resources");
    }
  };

  if (loading) {
    return <div>Loading alerts...</div>;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Active Alerts</h1>
        <p className="text-muted-foreground">
          Monitor and respond to emergency alerts for your station
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Alerts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {alerts.filter((a) => a.status === "active").length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dispatched</CardTitle>
            <Truck className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {alerts.filter((a) => a.status === "dispatched").length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">High Priority</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {alerts.filter((a) => a.urgency_level === "high").length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Available Vehicles
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {availableVehicles.length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Active Alerts Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Emergency Alerts
          </CardTitle>
          <CardDescription>
            Current emergency situations requiring response
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Smoke</TableHead>
                <TableHead>Urgency</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {alerts.map((alert) => (
                <TableRow key={alert.id}>
                  <TableCell>{alert.id}</TableCell>
                  <TableCell>{alert.smoke_level ?? "N/A"}</TableCell>
                  <TableCell>
                    <Badge variant={getPriorityColor(alert.urgency_level)}>
                      {alert.urgency_level}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusColor(alert.status)}>
                      {alert.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {alert.created_at
                      ? new Date(alert.created_at).toLocaleString()
                      : "N/A"}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="default"
                        size="sm"
                        onClick={() => handleAutoAssign(alert.id)}
                        title="Auto assign resources"
                      >
                        Auto Assign
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openGoogleMaps(alert)}
                        title="View location on map"
                      >
                        <MapPin className="w-4 h-4" />
                      </Button>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setSelectedAlert(alert)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle className="flex items-center gap-2">
                              <AlertTriangle className="w-5 h-5" /> Alert{" "}
                              {selectedAlert?.id}
                            </DialogTitle>
                            <DialogDescription>
                              Emergency alert details and response options
                            </DialogDescription>
                          </DialogHeader>
                          {selectedAlert && (
                            <div className="space-y-6">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <h4 className="font-medium text-sm text-muted-foreground">
                                    Device
                                  </h4>
                                  <p className="text-sm">
                                    {selectedAlert.device_id}
                                  </p>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm text-muted-foreground">
                                    Location
                                  </h4>
                                  <p className="text-sm">
                                    {selectedAlert.latitude &&
                                    selectedAlert.longitude
                                      ? `${selectedAlert.latitude}, ${selectedAlert.longitude}`
                                      : "N/A"}
                                  </p>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm text-muted-foreground">
                                    Smoke Level
                                  </h4>
                                  <p className="text-sm">
                                    {selectedAlert.smoke_level ?? "N/A"}
                                  </p>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm text-muted-foreground">
                                    Urgency
                                  </h4>
                                  <Badge
                                    variant={getPriorityColor(
                                      selectedAlert.urgency_level
                                    )}
                                  >
                                    {selectedAlert.urgency_level}
                                  </Badge>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm text-muted-foreground">
                                    Status
                                  </h4>
                                  <Badge
                                    variant={getStatusColor(
                                      selectedAlert.status
                                    )}
                                  >
                                    {selectedAlert.status}
                                  </Badge>
                                </div>
                                <div>
                                  <h4 className="font-medium text-sm text-muted-foreground">
                                    Created
                                  </h4>
                                  <p className="text-sm">
                                    {selectedAlert.created_at
                                      ? new Date(
                                          selectedAlert.created_at
                                        ).toLocaleString()
                                      : "N/A"}
                                  </p>
                                </div>
                              </div>
                              <div className="flex gap-4">
                                <Button
                                  variant="outline"
                                  onClick={() => openGoogleMaps(selectedAlert)}
                                  className="flex items-center gap-2 bg-transparent"
                                >
                                  <MapPin className="w-4 h-4" /> View on Map
                                </Button>
                                <Dialog
                                  open={isDispatchOpen}
                                  onOpenChange={setIsDispatchOpen}
                                >
                                  <DialogTrigger asChild>
                                    <Button className="flex items-center gap-2">
                                      <Truck className="w-4 h-4" /> Dispatch
                                      Vehicle
                                    </Button>
                                  </DialogTrigger>
                                  <DialogContent>
                                    <DialogHeader>
                                      <DialogTitle>
                                        Dispatch Vehicle
                                      </DialogTitle>
                                      <DialogDescription>
                                        Select a vehicle to dispatch to alert{" "}
                                        {selectedAlert.id}
                                      </DialogDescription>
                                    </DialogHeader>
                                    <div className="space-y-4">
                                      {availableVehicles.map((vehicle) => (
                                        <div
                                          key={vehicle.id}
                                          className="flex items-center justify-between p-3 border rounded-lg"
                                        >
                                          <div>
                                            <p className="font-medium">
                                              {vehicle.vehicle_number}
                                            </p>
                                            <p className="text-sm text-muted-foreground">
                                              {vehicle.vehicle_type}
                                            </p>
                                          </div>
                                          <Button
                                            size="sm"
                                            onClick={() =>
                                              handleDispatchVehicle(
                                                selectedAlert.id,
                                                vehicle.id
                                              )
                                            }
                                          >
                                            Dispatch
                                          </Button>
                                        </div>
                                      ))}
                                    </div>
                                  </DialogContent>
                                </Dialog>
                                <Button
                                  variant="outline"
                                  onClick={() =>
                                    handleResolveAlert(selectedAlert.id)
                                  }
                                  className="flex items-center gap-2 bg-transparent"
                                >
                                  <CheckCircle className="w-4 h-4" /> Mark
                                  Resolved
                                </Button>
                              </div>
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleResolveAlert(alert.id)}
                      >
                        <CheckCircle className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Quick Dispatch Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Available Vehicles
          </CardTitle>
          <CardDescription>
            Vehicles ready for immediate dispatch
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {availableVehicles.map((vehicle) => (
              <div key={vehicle.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{vehicle.vehicle_number}</h4>
                  <Badge variant="default" className="text-xs">
                    {vehicle.status}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  {vehicle.vehicle_type}
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1 text-sm">
                    <Users className="w-3 h-3" />
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() =>
                      handleDispatchVehicle(alerts[0]?.id, vehicle.id)
                    }
                  >
                    Quick Dispatch
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
