"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Users,
  Truck,
  AlertTriangle,
  Activity,
  TrendingUp,
  Clock,
  Shield,
  Wrench,
} from "lucide-react";
import { toast } from "sonner";

export default function StationManagerDashboard() {
  const [stationInfo, setStationInfo] = useState(null);
  const [stats, setStats] = useState(null);
  const [recentActivities, setRecentActivities] = useState([]);
  const [vehicleStatus, setVehicleStatus] = useState([]);
  const [shiftPersonnel, setShiftPersonnel] = useState([]);
  const [teams, setTeams] = useState([]);
  const [dutyStatus, setDutyStatus] = useState([]);
  const [vehicleDrivers, setVehicleDrivers] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem("token");
      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";

      // Fetch station info
      const stationResponse = await fetch(
        `${backendUrl}/api/profile/station-info`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (stationResponse.ok) {
        const stationData = await stationResponse.json();
        setStationInfo(stationData.station);
      }

      // Fetch stats
      const statsResponse = await fetch(
        `${backendUrl}/api/dashboard/station-manager/stats`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData);
      }

      // Fetch recent activities
      const activitiesResponse = await fetch(
        `${backendUrl}/api/dashboard/station-manager/recent-activities`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (activitiesResponse.ok) {
        const activitiesData = await activitiesResponse.json();
        setRecentActivities(activitiesData.activities);
      }

      // Fetch vehicle status
      const vehiclesResponse = await fetch(
        `${backendUrl}/api/dashboard/station-manager/vehicles`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (vehiclesResponse.ok) {
        const vehiclesData = await vehiclesResponse.json();
        setVehicleStatus(vehiclesData.vehicles);
      }

      // Fetch personnel
      const personnelResponse = await fetch(
        `${backendUrl}/api/dashboard/station-manager/personnel`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (personnelResponse.ok) {
        const personnelData = await personnelResponse.json();
        setShiftPersonnel(personnelData.personnel);
      }

      // Fetch teams data
      const teamsResponse = await fetch(`${backendUrl}/api/teams/teams`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (teamsResponse.ok) {
        const teamsData = await teamsResponse.json();
        setTeams(teamsData || []);
      }

      // Fetch duty status
      const dutyResponse = await fetch(`${backendUrl}/api/teams/duty-status`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (dutyResponse.ok) {
        const dutyData = await dutyResponse.json();
        setDutyStatus(dutyData || []);
      }

      // Fetch vehicle drivers for each vehicle
      const driversData = {};
      for (const vehicle of vehicleStatus) {
        try {
          const driversResponse = await fetch(
            `${backendUrl}/api/teams/vehicles/${vehicle.id}/drivers`,
            {
              headers: { Authorization: `Bearer ${token}` },
            }
          );
          if (driversResponse.ok) {
            const drivers = await driversResponse.json();
            driversData[vehicle.id] = drivers;
          }
        } catch (error) {
          console.error(`Failed to fetch drivers for vehicle ${vehicle.id}`);
        }
      }
      setVehicleDrivers(driversData);
    } catch (error) {
      toast.error("Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };

  const handleClockIn = async (userId, teamId = null) => {
    try {
      const token = localStorage.getItem("token");
      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";

      const response = await fetch(`${backendUrl}/api/teams/clock-in`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          user_ids: [userId],
          team_id: teamId,
        }),
      });

      if (response.ok) {
        toast.success("Successfully clocked in");
        fetchDashboardData(); // Refresh data
      } else {
        toast.error("Failed to clock in");
      }
    } catch (error) {
      toast.error("Error clocking in");
    }
  };

  const handleClockOut = async (userId) => {
    try {
      const token = localStorage.getItem("token");
      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";

      const response = await fetch(
        `${backendUrl}/api/teams/clock-out/${userId}`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        toast.success("Successfully clocked out");
        fetchDashboardData(); // Refresh data
      } else {
        toast.error("Failed to clock out");
      }
    } catch (error) {
      toast.error("Error clocking out");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        Loading dashboard...
      </div>
    );
  }

  const statsCards = stats
    ? [
        {
          title: "Active Responders",
          value: stats.active_responders.toString(),
          description: "Currently on duty",
          icon: Users,
          color: "text-blue-600",
        },
        {
          title: "Available Vehicles",
          value: stats.available_vehicles.toString(),
          description: `${stats.vehicle_availability_percentage}% availability`,
          icon: Truck,
          color: "text-green-600",
        },
        {
          title: "Active Alerts",
          value: stats.active_alerts.toString(),
          description: "Requiring response",
          icon: AlertTriangle,
          color: "text-red-600",
        },
        {
          title: "Response Time",
          value: `${stats.avg_response_time_minutes}m`,
          description: "Average this month",
          icon: Clock,
          color: "text-orange-600",
        },
      ]
    : [];

  // Calculate performance metrics from real data
  const responseTimeGoal = stats
    ? Math.min(100, (5 / Math.max(stats.avg_response_time_minutes, 0.1)) * 100)
    : 0;
  const vehicleAvailabilityPercentage = stats
    ? stats.vehicle_availability_percentage
    : 0;
  const personnelCoverage =
    stats && shiftPersonnel.length > 0
      ? Math.min(100, (shiftPersonnel.length / 20) * 100)
      : 0;

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
      case "dispatched":
        return "destructive";
      case "available":
      case "on-duty":
        return "default";
      case "maintenance":
      case "scheduled":
        return "secondary";
      case "completed":
        return "outline";
      default:
        return "secondary";
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "text-red-600";
      case "medium":
        return "text-orange-600";
      case "low":
        return "text-green-600";
      default:
        return "text-gray-600";
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">
          {stationInfo ? `${stationInfo.name} Dashboard` : "Station Dashboard"}
        </h1>
        <p className="text-muted-foreground">
          {stationInfo
            ? `${stationInfo.district} • ${stationInfo.region}`
            : "Loading station information..."}
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest station activities and dispatches
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.length > 0 ? (
                recentActivities.map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-start gap-3 p-3 rounded-lg border"
                  >
                    <div
                      className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${getPriorityColor(
                        activity.priority
                      ).replace("text-", "bg-")}`}
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium">{activity.message}</p>
                      <p className="text-xs text-muted-foreground">
                        {activity.time}
                      </p>
                    </div>
                    <Badge
                      variant={getStatusColor(activity.status)}
                      className="text-xs"
                    >
                      {activity.status}
                    </Badge>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">
                    No recent activities in the last 24 hours
                  </p>
                </div>
              )}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full bg-transparent">
                View All Activity
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common station management tasks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button
                className="w-full justify-start bg-transparent"
                variant="outline"
              >
                <AlertTriangle className="w-4 h-4 mr-2" />
                View Active Alerts
              </Button>
              <Button
                className="w-full justify-start bg-transparent"
                variant="outline"
              >
                <Truck className="w-4 h-4 mr-2" />
                Manage Vehicles
              </Button>
              <Button
                className="w-full justify-start bg-transparent"
                variant="outline"
              >
                <Users className="w-4 h-4 mr-2" />
                Manage Personnel
              </Button>
              <Button
                className="w-full justify-start bg-transparent"
                variant="outline"
              >
                <Wrench className="w-4 h-4 mr-2" />
                Schedule Maintenance
              </Button>
              <Button
                className="w-full justify-start bg-transparent"
                variant="outline"
              >
                <Activity className="w-4 h-4 mr-2" />
                Generate Reports
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Vehicle Status and Personnel */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Truck className="h-5 w-5" />
              Vehicle Status
            </CardTitle>
            <CardDescription>
              Current status of all station vehicles
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {vehicleStatus.length > 0 ? (
                vehicleStatus.map((vehicle, index) => (
                  <div
                    key={vehicle.id || index}
                    className="p-3 rounded-lg border"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <p className="font-medium text-sm">{vehicle.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {vehicle.type}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge
                          variant={getStatusColor(vehicle.status)}
                          className="text-xs mb-1"
                        >
                          {vehicle.status}
                        </Badge>
                        <p className="text-xs text-muted-foreground">
                          {vehicle.location}
                        </p>
                      </div>
                    </div>

                    {/* Show assigned drivers */}
                    {vehicleDrivers[vehicle.id] &&
                      vehicleDrivers[vehicle.id].length > 0 && (
                        <div className="mt-2 pt-2 border-t">
                          <p className="text-xs font-medium text-muted-foreground mb-1">
                            Assigned Drivers:
                          </p>
                          <div className="space-y-1">
                            {vehicleDrivers[vehicle.id].map((assignment) => {
                              const isOnDuty = dutyStatus.some(
                                (shift) =>
                                  shift.user_id === assignment.driver_id &&
                                  shift.vehicle_id === vehicle.id
                              );
                              return (
                                <div
                                  key={assignment.id}
                                  className="flex items-center justify-between"
                                >
                                  <span className="text-xs">
                                    {assignment.driver?.full_name ||
                                      "Unknown Driver"}
                                  </span>
                                  <Badge
                                    variant={isOnDuty ? "default" : "secondary"}
                                    className="text-xs"
                                  >
                                    {isOnDuty ? "On Duty" : "Off Duty"}
                                  </Badge>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Truck className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">
                    No vehicles assigned to this station
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Current Shift Personnel
            </CardTitle>
            <CardDescription>Personnel currently on duty</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {shiftPersonnel.length > 0 ? (
                shiftPersonnel.map((person, index) => (
                  <div
                    key={person.id || index}
                    className="flex items-center justify-between p-3 rounded-lg border"
                  >
                    <div className="flex-1">
                      <p className="font-medium text-sm">{person.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {person.role}
                        {person.team && ` • ${person.team}`}
                      </p>
                      {person.contact && (
                        <p className="text-xs text-muted-foreground">
                          {person.contact}
                        </p>
                      )}
                    </div>
                    <div className="text-right">
                      <Badge
                        variant={getStatusColor(person.status)}
                        className="text-xs mb-1"
                      >
                        {person.status}
                      </Badge>
                      <p className="text-xs text-muted-foreground mb-2">
                        {person.shift}
                      </p>
                      <div className="flex gap-1">
                        {person.status === "off-duty" ? (
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-xs h-6 px-2"
                            onClick={() => handleClockIn(person.id)}
                          >
                            Clock In
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-xs h-6 px-2"
                            onClick={() => handleClockOut(person.id)}
                          >
                            Clock Out
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Shield className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No personnel currently on duty</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Station Performance</CardTitle>
          <CardDescription>
            Key performance indicators for this month
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Response Time Goal</span>
                <span className="text-sm text-muted-foreground">
                  {Math.round(responseTimeGoal)}%
                </span>
              </div>
              <Progress value={responseTimeGoal} className="h-2" />
              <p className="text-xs text-muted-foreground">
                Current avg: {stats?.avg_response_time_minutes || 0}m (Target:
                Under 5m)
              </p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  Vehicle Availability
                </span>
                <span className="text-sm text-muted-foreground">
                  {Math.round(vehicleAvailabilityPercentage)}%
                </span>
              </div>
              <Progress value={vehicleAvailabilityPercentage} className="h-2" />
              <p className="text-xs text-muted-foreground">
                {stats?.available_vehicles || 0} of {stats?.total_vehicles || 0}{" "}
                vehicles available
              </p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Personnel Coverage</span>
                <span className="text-sm text-muted-foreground">
                  {Math.round(personnelCoverage)}%
                </span>
              </div>
              <Progress value={personnelCoverage} className="h-2" />
              <p className="text-xs text-muted-foreground">
                {shiftPersonnel.length} personnel on duty
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
