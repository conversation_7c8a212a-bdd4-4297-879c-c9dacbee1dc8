"use client";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";

export default function LeafletMap({ refillPoints, mapClick, handleMapClick }) {
  return (
    <MapContainer
      center={[-11.4596, 34.0194]}
      zoom={7}
      style={{ height: "100%", width: "100%" }}
      whenCreated={(map) => map.on("click", handleMapClick)}
    >
      <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
      {refillPoints?.map((point) => (
        <Marker key={point.id} position={[point.latitude, point.longitude]}>
          <Popup>
            <b>{point.name || "Unnamed"}</b>
            <br />
            {point.description}
          </Popup>
        </Marker>
      ))}
      {mapClick && (
        <Marker position={mapClick}>
          <Popup>New Refill Point</Popup>
        </Marker>
      )}
    </MapContainer>
  );
}
