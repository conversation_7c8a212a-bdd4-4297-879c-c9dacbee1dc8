// "use client";

// import { useEffect, useRef, useState } from "react";
// import { Plus } from "lucide-react";
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from "@/components/ui/table";
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";

// export default function WaterRefillPointsPage() {
//   const mapRef = useRef(null);
//   const mapInstanceRef = useRef(null);
//   const tempMarkerRef = useRef(null);
//   const vectorLayerRef = useRef(null);

//   const [refillPoints, setRefillPoints] = useState([]);
//   const [showAdd, setShowAdd] = useState(false);
//   const [newPoint, setNewPoint] = useState({
//     name: "",
//     latitude: "",
//     longitude: "",
//     description: "",
//   });

//   const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";

//   // Fetch points from backend
//   const fetchPoints = async () => {
//     try {
//       const token = localStorage.getItem("token");
//       const res = await fetch(`${backendUrl}/water-refill/`, {
//         headers: { Authorization: `Bearer ${token}` },
//       });
//       if (res.ok) {
//         setRefillPoints(await res.json());
//       }
//     } catch (error) {
//       console.error("Error fetching points:", error);
//     }
//   };

//   useEffect(() => {
//     fetchPoints();
//   }, []);

//   // Initialize map
//   useEffect(() => {
//     if (!mapRef.current || mapInstanceRef.current) return;

//     const vectorSource = new window.ol.source.Vector();
//     const vectorLayer = new window.ol.layer.Vector({
//       source: vectorSource,
//       style: new window.ol.style.Style({
//         image: new window.ol.style.Circle({
//           radius: 8,
//           fill: new window.ol.style.Fill({ color: "#3b82f6" }),
//           stroke: new window.ol.style.Stroke({ color: "#ffffff", width: 2 }),
//         }),
//       }),
//     });

//     const map = new window.ol.Map({
//       target: mapRef.current,
//       layers: [
//         new window.ol.layer.Tile({
//           source: new window.ol.source.OSM(),
//         }),
//         vectorLayer,
//       ],
//       view: new window.ol.View({
//         center: window.ol.proj.fromLonLat([34.0194, -11.4596]),
//         zoom: 7,
//       }),
//     });

//     map.on("click", (evt) => {
//       const [lon, lat] = window.ol.proj.toLonLat(evt.coordinate);

//       // Remove old temporary marker
//       if (tempMarkerRef.current) {
//         vectorSource.removeFeature(tempMarkerRef.current);
//       }

//       // Add new temporary marker
//       const tempMarker = new window.ol.Feature({
//         geometry: new window.ol.geom.Point(evt.coordinate),
//         type: "temp",
//       });

//       tempMarker.setStyle(
//         new window.ol.style.Style({
//           image: new window.ol.style.Circle({
//             radius: 8,
//             fill: new window.ol.style.Fill({ color: "#ef4444" }),
//             stroke: new window.ol.style.Stroke({ color: "#ffffff", width: 2 }),
//           }),
//         })
//       );

//       vectorSource.addFeature(tempMarker);
//       tempMarkerRef.current = tempMarker;

//       setNewPoint((prev) => ({
//         ...prev,
//         latitude: lat.toFixed(6),
//         longitude: lon.toFixed(6),
//       }));
//       setShowAdd(true);
//     });

//     mapInstanceRef.current = map;
//     vectorLayerRef.current = vectorLayer;

//     return () => {
//       if (mapInstanceRef.current) {
//         mapInstanceRef.current.setTarget(null);
//         mapInstanceRef.current = null;
//       }
//       vectorLayerRef.current = null;
//       tempMarkerRef.current = null;
//     };
//   }, []);

//   // Update permanent markers when refillPoints change
//   useEffect(() => {
//     if (!vectorLayerRef.current) return;
//     const vectorSource = vectorLayerRef.current.getSource();
//     if (!vectorSource) return;

//     // Clear old permanent markers
//     vectorSource.getFeatures().forEach((f) => {
//       if (f.get("type") !== "temp") {
//         vectorSource.removeFeature(f);
//       }
//     });

//     // Add markers for fetched refill points
//     refillPoints.forEach((point) => {
//       if (!point.latitude || !point.longitude) return;

//       const marker = new window.ol.Feature({
//         geometry: new window.ol.geom.Point(
//           window.ol.proj.fromLonLat([
//             parseFloat(point.longitude),
//             parseFloat(point.latitude),
//           ])
//         ),
//         name: point.name,
//         description: point.description,
//         type: "permanent",
//       });

//       vectorSource.addFeature(marker);
//     });
//   }, [refillPoints]);

//   const handleAdd = async () => {
//     try {
//       const token = localStorage.getItem("token");
//       const res = await fetch(`${backendUrl}/water-refill/`, {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//           Authorization: `Bearer ${token}`,
//         },
//         body: JSON.stringify({
//           ...newPoint,
//           latitude: parseFloat(newPoint.latitude),
//           longitude: parseFloat(newPoint.longitude),
//         }),
//       });
//       if (res.ok) {
//         setShowAdd(false);
//         setNewPoint({ name: "", latitude: "", longitude: "", description: "" });
//         fetchPoints();
//       }
//     } catch (error) {
//       console.error("Error adding point:", error);
//     }
//   };

//   const handleCancel = () => {
//     if (tempMarkerRef.current && vectorLayerRef.current) {
//       vectorLayerRef.current.getSource()?.removeFeature(tempMarkerRef.current);
//       tempMarkerRef.current = null;
//     }
//     setShowAdd(false);
//     setNewPoint({ name: "", latitude: "", longitude: "", description: "" });
//   };

//   return (
//     <div className="space-y-6">
//       <Card>
//         <CardHeader>
//           <CardTitle>Water Refill Points</CardTitle>
//         </CardHeader>
//         <CardContent className="space-y-4">
//           {/* Map */}
//           <div className="w-full">
//             <div
//               ref={mapRef}
//               className="w-full border rounded-md"
//               style={{ height: "350px" }}
//             />
//             <p className="text-sm text-gray-600 mt-2">
//               Click on the map to add a new refill point
//             </p>
//           </div>

//           {/* Add Button */}
//           <Button onClick={() => setShowAdd(true)} className="w-fit">
//             <Plus className="w-4 h-4 mr-2" /> Add Refill Point
//           </Button>

//           {/* Add Form */}
//           {showAdd && (
//             <Card className="border-2 border-blue-200">
//               <CardContent className="p-4">
//                 <h3 className="text-lg font-semibold mb-4">
//                   Add New Refill Point
//                 </h3>
//                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
//                   <div>
//                     <Label htmlFor="name">Name *</Label>
//                     <Input
//                       id="name"
//                       placeholder="Enter point name"
//                       value={newPoint.name}
//                       onChange={(e) =>
//                         setNewPoint({ ...newPoint, name: e.target.value })
//                       }
//                     />
//                   </div>
//                   <div>
//                     <Label htmlFor="description">Description</Label>
//                     <Input
//                       id="description"
//                       placeholder="Enter description"
//                       value={newPoint.description}
//                       onChange={(e) =>
//                         setNewPoint({
//                           ...newPoint,
//                           description: e.target.value,
//                         })
//                       }
//                     />
//                   </div>
//                   <div>
//                     <Label htmlFor="latitude">Latitude *</Label>
//                     <Input
//                       id="latitude"
//                       placeholder="Enter latitude"
//                       value={newPoint.latitude}
//                       onChange={(e) =>
//                         setNewPoint({ ...newPoint, latitude: e.target.value })
//                       }
//                     />
//                   </div>
//                   <div>
//                     <Label htmlFor="longitude">Longitude *</Label>
//                     <Input
//                       id="longitude"
//                       placeholder="Enter longitude"
//                       value={newPoint.longitude}
//                       onChange={(e) =>
//                         setNewPoint({ ...newPoint, longitude: e.target.value })
//                       }
//                     />
//                   </div>
//                 </div>
//                 <div className="flex gap-2">
//                   <Button
//                     onClick={handleAdd}
//                     disabled={
//                       !newPoint.name ||
//                       !newPoint.latitude ||
//                       !newPoint.longitude
//                     }
//                   >
//                     Save Point
//                   </Button>
//                   <Button variant="outline" onClick={handleCancel}>
//                     Cancel
//                   </Button>
//                 </div>
//               </CardContent>
//             </Card>
//           )}

//           {/* Points Table */}
//           <div className="w-full">
//             <h3 className="text-lg font-semibold mb-3">
//               Existing Refill Points
//             </h3>
//             {refillPoints.length > 0 ? (
//               <div className="border rounded-md">
//                 <Table>
//                   <TableHeader>
//                     <TableRow>
//                       <TableHead>Name</TableHead>
//                       <TableHead>Description</TableHead>
//                       <TableHead>Latitude</TableHead>
//                       <TableHead>Longitude</TableHead>
//                     </TableRow>
//                   </TableHeader>
//                   <TableBody>
//                     {refillPoints.map((point) => (
//                       <TableRow key={point.id}>
//                         <TableCell className="font-medium">
//                           {point.name}
//                         </TableCell>
//                         <TableCell>
//                           {point.description || "No description"}
//                         </TableCell>
//                         <TableCell>{point.latitude}</TableCell>
//                         <TableCell>{point.longitude}</TableCell>
//                       </TableRow>
//                     ))}
//                   </TableBody>
//                 </Table>
//               </div>
//             ) : (
//               <div className="text-center py-8 text-gray-500 border rounded-md">
//                 <p>
//                   No refill points found. Click on the map to add your first
//                   point!
//                 </p>
//               </div>
//             )}
//           </div>
//         </CardContent>
//       </Card>
//     </div>
//   );
// }

"use client";
import { useEffect, useRef, useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";

export default function WaterRefillPointsViewPage() {
  const mapRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const vectorLayerRef = useRef(null);
  const [refillPoints, setRefillPoints] = useState([]);
  const [loading, setLoading] = useState(true);
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";

  useEffect(() => {
    fetchPoints();
  }, []);

  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    // Create vector source and layer for markers
    const vectorSource = new window.ol.source.Vector();
    const vectorLayer = new window.ol.layer.Vector({
      source: vectorSource,
      style: new window.ol.style.Style({
        image: new window.ol.style.Circle({
          radius: 8,
          fill: new window.ol.style.Fill({ color: "#3b82f6" }),
          stroke: new window.ol.style.Stroke({ color: "#ffffff", width: 2 }),
        }),
      }),
    });

    // Create simple map with OSM tiles and vector layer
    const mapEl = new window.ol.Map({
      target: mapRef.current,
      layers: [
        new window.ol.layer.Tile({
          source: new window.ol.source.OSM(),
        }),
        vectorLayer,
      ],
      view: new window.ol.View({
        center: window.ol.proj.fromLonLat([34.0194, -11.4596]),
        zoom: 7,
      }),
    });

    // Optional: Add popup on marker click to show point details
    mapEl.on("click", (evt) => {
      const feature = mapEl.forEachFeatureAtPixel(
        evt.pixel,
        (feature) => feature
      );
      if (feature) {
        const name = feature.get("name");
        const description = feature.get("description");
        if (name) {
          // You could add a popup here or show details in some other way
          console.log(`Clicked on: ${name} - ${description}`);
        }
      }
    });

    mapInstanceRef.current = mapEl;
    vectorLayerRef.current = vectorLayer;

    // Cleanup function
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.setTarget(null);
        mapInstanceRef.current = null;
      }
      vectorLayerRef.current = null;
    };
  }, []);

  // Effect to update markers when refill points change
  useEffect(() => {
    if (!vectorLayerRef.current || !refillPoints.length) return;

    const vectorSource = vectorLayerRef.current.getSource();

    // Clear existing markers
    vectorSource.clear();

    // Add markers for all refill points
    refillPoints.forEach((point) => {
      const marker = new window.ol.Feature({
        geometry: new window.ol.geom.Point(
          window.ol.proj.fromLonLat([point.longitude, point.latitude])
        ),
        name: point.name,
        description: point.description,
        id: point.id,
      });

      vectorSource.addFeature(marker);
    });

    // Auto-fit map to show all markers if there are points
    if (refillPoints.length > 0) {
      const extent = vectorSource.getExtent();
      if (extent && extent.every((coord) => isFinite(coord))) {
        mapInstanceRef.current.getView().fit(extent, {
          padding: [50, 50, 50, 50],
          maxZoom: 15,
        });
      }
    }
  }, [refillPoints]);

  const fetchPoints = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem("token");
      const res = await fetch(`${backendUrl}/water-refill/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (res.ok) {
        const points = await res.json();
        setRefillPoints(points);
      } else {
        console.error("Failed to fetch refill points");
      }
    } catch (error) {
      console.error("Error fetching points:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Water Refill Points Map</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Map Container */}
          <div className="w-full">
            <div
              ref={mapRef}
              className="w-full border rounded-md"
              style={{ height: "450px" }}
            />
            <p className="text-sm text-gray-600 mt-2">
              {refillPoints.length > 0
                ? `Showing ${refillPoints.length} water refill point${
                    refillPoints.length !== 1 ? "s" : ""
                  }`
                : "Loading refill points..."}
            </p>
          </div>

          {/* Points Table */}
          <div className="w-full">
            <h3 className="text-lg font-semibold mb-3">
              Available Refill Points
            </h3>
            {loading ? (
              <div className="text-center py-8 text-gray-500 border rounded-md">
                <p>Loading refill points...</p>
              </div>
            ) : refillPoints.length > 0 ? (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Latitude</TableHead>
                      <TableHead>Longitude</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {refillPoints.map((point) => (
                      <TableRow key={point.id}>
                        <TableCell className="font-medium">
                          {point.name}
                        </TableCell>
                        <TableCell>
                          {point.description || "No description"}
                        </TableCell>
                        <TableCell>{point.latitude}</TableCell>
                        <TableCell>{point.longitude}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 border rounded-md">
                <p>No refill points available at the moment.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
