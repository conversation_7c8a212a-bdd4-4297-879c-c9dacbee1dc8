"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Plus,
  Search,
  Edit,
  Wrench,
  Truck,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  User,
  Trash2,
} from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

export default function VehicleManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editVehicle, setEditVehicle] = useState(null);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [vehicleDrivers, setVehicleDrivers] = useState([]);
  const [editForm, setEditForm] = useState({
    vehicle_number: "",
    vehicle_type: "",
    status: "available",
    station_id: 1,
  });

  // Fetch vehicles from backend
  useEffect(() => {
    async function fetchVehicles() {
      setLoading(true);
      try {
        const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
        const token =
          typeof window !== "undefined" ? localStorage.getItem("token") : null;
        const res = await fetch(`${backendUrl}/api/station-manager/vehicles`, {
          headers: {
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
        });

        console.log("Toke:", token);
        const data = await res.json();
        console.log("response: ", data);
        if (!res.ok) throw new Error("Failed to fetch vehicles");

        setVehicles(data);
      } catch (e) {
        setVehicles([]);
      } finally {
        setLoading(false);
      }
    }
    fetchVehicles();
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case "available":
        return "default";
      case "dispatched":
        return "destructive";
      case "maintenance":
        return "secondary";
      default:
        return "secondary";
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "available":
        return <CheckCircle className="w-4 h-4" />;
      case "dispatched":
        return <AlertTriangle className="w-4 h-4" />;
      case "maintenance":
        return <Wrench className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const filteredVehicles = vehicles.filter((vehicle) => {
    const matchesSearch =
      vehicle?.vehicle_number
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      vehicle?.vehicle_type?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" || vehicle.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleMaintenanceSchedule = (vehicleName) => {
    toast.success(`Maintenance scheduled for ${vehicleName}`);
  };

  const handleDispatch = (vehicleName) => {
    toast.success(`${vehicleName} marked as dispatched`);
  };

  const handleEditVehicle = (vehicle) => {
    setEditVehicle(vehicle);
    setEditForm({
      vehicle_number: vehicle.vehicle_number || "",
      vehicle_type: vehicle.vehicle_type || "",
      status: vehicle.status || "available",
      station_id: vehicle.station_id || 1,
    });
    setEditModalOpen(true);
  };

  const handleEditFormChange = (field, value) => {
    setEditForm((prev) => ({ ...prev, [field]: value }));
  };

  const handleUpdateVehicle = async (e) => {
    e.preventDefault();
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
    const token =
      typeof window !== "undefined" ? localStorage.getItem("token") : null;
    try {
      const res = await fetch(
        `${backendUrl}/api/station-manager/vehicles/${editVehicle.id}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
          body: JSON.stringify(editForm),
        }
      );
      if (!res.ok) throw new Error("Failed to update vehicle");
      const updated = await res.json();
      setVehicles((prev) =>
        prev.map((v) => (v.id === updated.id ? { ...v, ...updated } : v))
      );
      toast.success("Vehicle updated successfully");
      setEditModalOpen(false);
      setEditVehicle(null);
    } catch {
      toast.error("Failed to update vehicle");
    }
  };

  const handleViewVehicle = async (vehicle) => {
    setSelectedVehicle(vehicle);
    setViewModalOpen(true);

    // Fetch drivers assigned to this vehicle
    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
      const token = localStorage.getItem("token");
      const res = await fetch(
        `${backendUrl}/api/teams/vehicles/${vehicle.id}/drivers`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (res.ok) {
        const data = await res.json();
        setVehicleDrivers(data);
      } else {
        setVehicleDrivers([]);
      }
    } catch (error) {
      console.error("Error fetching vehicle drivers:", error);
      setVehicleDrivers([]);
    }
  };

  const handleDeleteVehicle = async (vehicleId) => {
    const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
    const token =
      typeof window !== "undefined" ? localStorage.getItem("token") : null;
    try {
      const res = await fetch(
        `${backendUrl}/api/station-manager/vehicles/${vehicleId}`,
        {
          method: "DELETE",
          headers: {
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
        }
      );
      if (!res.ok) throw new Error("Failed to delete vehicle");
      setVehicles((prev) => prev.filter((v) => v.id !== vehicleId));
      toast.success("Vehicle deleted");
    } catch {
      toast.error("Failed to delete vehicle");
    }
  };

  const availableVehicles = vehicles.filter(
    (v) => v.status === "available"
  ).length;
  const dispatchedVehicles = vehicles.filter(
    (v) => v.status === "dispatched"
  ).length;
  const maintenanceVehicles = vehicles.filter(
    (v) => v.status === "maintenance"
  ).length;
  const totalVehicles = vehicles.length;

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Vehicle Management</h1>
        <p className="text-muted-foreground">
          Manage all vehicles assigned to Station 5
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Vehicles
            </CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalVehicles}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {availableVehicles}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dispatched</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {dispatchedVehicles}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Maintenance</CardTitle>
            <Wrench className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">
              {maintenanceVehicles}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Vehicle Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Station Vehicles</CardTitle>
              <CardDescription>
                Monitor and manage all station vehicles and their status
              </CardDescription>
            </div>
            <Link href="/station-manager/vehicles/add">
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Add Vehicle
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search vehicles..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="available">Available</SelectItem>
                <SelectItem value="dispatched">Dispatched</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Vehicle Number</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredVehicles.map((vehicle) => (
                <TableRow key={vehicle.id}>
                  <TableCell className="font-medium">
                    {vehicle.vehicle_number}
                  </TableCell>
                  <TableCell>{vehicle.vehicle_type}</TableCell>
                  <TableCell>
                    <Badge
                      variant={getStatusColor(vehicle.status)}
                      className="flex items-center gap-1 w-fit"
                    >
                      {getStatusIcon(vehicle.status)}
                      {vehicle.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewVehicle(vehicle)}
                        title="View Details"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditVehicle(vehicle)}
                        title="Edit Vehicle"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteVehicle(vehicle.id)}
                        title="Delete Vehicle"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Dialog open={editModalOpen} onOpenChange={setEditModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Vehicle</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleUpdateVehicle} className="space-y-4">
            <div>
              <Label htmlFor="vehicle_number">Vehicle Number</Label>
              <Input
                id="vehicle_number"
                value={editForm.vehicle_number}
                onChange={(e) =>
                  handleEditFormChange("vehicle_number", e.target.value)
                }
                required
              />
            </div>
            <div>
              <Label htmlFor="vehicle_type">Vehicle Type</Label>
              <Input
                id="vehicle_type"
                value={editForm.vehicle_type}
                onChange={(e) =>
                  handleEditFormChange("vehicle_type", e.target.value)
                }
                required
              />
            </div>
            <div>
              <Label htmlFor="status">Status</Label>
              <Select
                value={editForm.status}
                onValueChange={(v) => handleEditFormChange("status", v)}
              >
                <SelectTrigger id="status">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="available">Available</SelectItem>
                  <SelectItem value="dispatched">Dispatched</SelectItem>
                  <SelectItem value="maintenance">Maintenance</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setEditModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit">Update Vehicle</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Vehicle View Modal */}
      <Dialog open={viewModalOpen} onOpenChange={setViewModalOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Truck className="w-5 h-5" />
              Vehicle Details
            </DialogTitle>
          </DialogHeader>
          {selectedVehicle && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">
                    Vehicle Number
                  </h4>
                  <p className="font-medium">
                    {selectedVehicle.vehicle_number}
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">
                    Type
                  </h4>
                  <p>{selectedVehicle.vehicle_type}</p>
                </div>
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">
                    Status
                  </h4>
                  <Badge
                    variant={getStatusColor(selectedVehicle.status)}
                    className="flex items-center gap-1 w-fit"
                  >
                    {getStatusIcon(selectedVehicle.status)}
                    {selectedVehicle.status}
                  </Badge>
                </div>
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground">
                    Station ID
                  </h4>
                  <p>{selectedVehicle.station_id}</p>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-3">
                  Assigned Drivers
                </h4>
                {vehicleDrivers.length > 0 ? (
                  <div className="space-y-2">
                    {vehicleDrivers.map((assignment) => (
                      <div
                        key={assignment.id}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          <User className="w-4 h-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium">
                              {assignment.driver.full_name}
                            </p>
                            <p className="text-sm text-muted-foreground">
                              {assignment.driver.email}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-muted-foreground">
                            Assigned:{" "}
                            {new Date(
                              assignment.assigned_at
                            ).toLocaleDateString()}
                          </p>
                          <Badge
                            variant={
                              assignment.is_active ? "default" : "secondary"
                            }
                          >
                            {assignment.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    No drivers assigned to this vehicle
                  </p>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
