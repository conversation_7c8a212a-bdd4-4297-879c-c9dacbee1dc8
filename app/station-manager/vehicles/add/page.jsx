"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ArrowLeft, Truck, Save } from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";

export default function AddVehicle() {
  const [vehicleData, setVehicleData] = useState({
    name: "",
    type: "",
    make: "",
    model: "",
    year: "",
    vin: "",
    licensePlate: "",
    mileage: "",
    fuelCapacity: "",
    pumpCapacity: "",
    ladderHeight: "",
    waterTankCapacity: "",
    foamTankCapacity: "",
    equipmentDetails: "",
    purchaseDate: "",
    warrantyExpiration: "",
    insurancePolicy: "",
    notes: "",
  });
  const [stationId, setStationId] = useState(null);

  useEffect(() => {
    async function fetchStationId() {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
      try {
        const token =
          typeof window !== "undefined" && localStorage.getItem("token");

        const res = await fetch(`${backendUrl}/station/vehicles`, {
          headers: {
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
        });
        if (!res.ok) throw new Error("Failed to fetch vehicles");
        const data = await res.json();
        if (data.length > 0 && data[0].station_id) {
          setStationId(data[0].station_id);
        } else {
          setStationId(null);
        }
      } catch (err) {
        setStationId(null);
      }
    }
    fetchStationId();
  }, []);

  const vehicleTypes = [
    "Fire Engine",
    "Ladder Truck",
    "Ambulance",
    "Rescue Vehicle",
    "Hazmat Unit",
    "Water Tender",
    "Command Vehicle",
    "Utility Vehicle",
  ];

  const handleInputChange = (field, value) => {
    setVehicleData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
      const token =
        typeof window !== "undefined" ? localStorage.getItem("token") : null;
      const payload = {
        vehicle_number: vehicleData.name,
        vehicle_type: vehicleData.type,
        station_id: JSON.parse(localStorage.getItem("user"))?.station_id, // get from logged in user
      };
      const res = await fetch(`${backendUrl}/station/vehicles`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...(token ? { Authorization: `Bearer ${token}` } : {}),
        },
        body: JSON.stringify(payload),
      });
      if (!res.ok) throw new Error("Failed to add vehicle");
      toast.success("Vehicle added successfully to Station");
      setVehicleData({
        name: "",
        type: "",
        make: "",
        model: "",
        year: "",
        vin: "",
        licensePlate: "",
        mileage: "",
        fuelCapacity: "",
        pumpCapacity: "",
        ladderHeight: "",
        waterTankCapacity: "",
        foamTankCapacity: "",
        equipmentDetails: "",
        purchaseDate: "",
        warrantyExpiration: "",
        insurancePolicy: "",
        notes: "",
      });
    } catch (err) {
      toast.error("Failed to add vehicle");
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/station-manager/vehicles">
          <Button variant="outline" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Vehicles
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold">Add New Vehicle</h1>
          <p className="text-muted-foreground">
            Add a new vehicle to Station 5 fleet
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Truck className="w-5 h-5" />
              Basic Information
            </CardTitle>
            <CardDescription>
              Enter the basic details of the vehicle
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Vehicle Name/ID *</Label>
                <Input
                  id="name"
                  placeholder="Engine 5-3"
                  value={vehicleData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="type">Vehicle Type *</Label>
                <Select
                  value={vehicleData.type}
                  onValueChange={(value) => handleInputChange("type", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select vehicle type" />
                  </SelectTrigger>
                  <SelectContent>
                    {vehicleTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="make">Make *</Label>
                <Input
                  id="make"
                  placeholder="Pierce, Ford, Freightliner"
                  value={vehicleData.make}
                  onChange={(e) => handleInputChange("make", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="model">Model *</Label>
                <Input
                  id="model"
                  placeholder="Enforcer, F-450, M2 106"
                  value={vehicleData.model}
                  onChange={(e) => handleInputChange("model", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="year">Year *</Label>
                <Input
                  id="year"
                  type="number"
                  placeholder="2024"
                  value={vehicleData.year}
                  onChange={(e) => handleInputChange("year", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="mileage">Current Mileage</Label>
                <Input
                  id="mileage"
                  type="number"
                  placeholder="0"
                  value={vehicleData.mileage}
                  onChange={(e) => handleInputChange("mileage", e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Vehicle Identification */}
        <Card>
          <CardHeader>
            <CardTitle>Vehicle Identification</CardTitle>
            <CardDescription>
              Legal and identification information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="vin">VIN Number *</Label>
                <Input
                  id="vin"
                  placeholder="1FDGF5GT8KEA12345"
                  value={vehicleData.vin}
                  onChange={(e) => handleInputChange("vin", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="licensePlate">License Plate</Label>
                <Input
                  id="licensePlate"
                  placeholder="FD-5001"
                  value={vehicleData.licensePlate}
                  onChange={(e) =>
                    handleInputChange("licensePlate", e.target.value)
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="purchaseDate">Purchase Date</Label>
                <Input
                  id="purchaseDate"
                  type="date"
                  value={vehicleData.purchaseDate}
                  onChange={(e) =>
                    handleInputChange("purchaseDate", e.target.value)
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="warrantyExpiration">Warranty Expiration</Label>
                <Input
                  id="warrantyExpiration"
                  type="date"
                  value={vehicleData.warrantyExpiration}
                  onChange={(e) =>
                    handleInputChange("warrantyExpiration", e.target.value)
                  }
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Technical Specifications */}
        <Card>
          <CardHeader>
            <CardTitle>Technical Specifications</CardTitle>
            <CardDescription>
              Vehicle capabilities and specifications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="fuelCapacity">Fuel Capacity (gallons)</Label>
                <Input
                  id="fuelCapacity"
                  type="number"
                  placeholder="50"
                  value={vehicleData.fuelCapacity}
                  onChange={(e) =>
                    handleInputChange("fuelCapacity", e.target.value)
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="pumpCapacity">Pump Capacity (GPM)</Label>
                <Input
                  id="pumpCapacity"
                  type="number"
                  placeholder="1500"
                  value={vehicleData.pumpCapacity}
                  onChange={(e) =>
                    handleInputChange("pumpCapacity", e.target.value)
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="waterTankCapacity">
                  Water Tank Capacity (gallons)
                </Label>
                <Input
                  id="waterTankCapacity"
                  type="number"
                  placeholder="750"
                  value={vehicleData.waterTankCapacity}
                  onChange={(e) =>
                    handleInputChange("waterTankCapacity", e.target.value)
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="foamTankCapacity">
                  Foam Tank Capacity (gallons)
                </Label>
                <Input
                  id="foamTankCapacity"
                  type="number"
                  placeholder="25"
                  value={vehicleData.foamTankCapacity}
                  onChange={(e) =>
                    handleInputChange("foamTankCapacity", e.target.value)
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="ladderHeight">Ladder Height (feet)</Label>
                <Input
                  id="ladderHeight"
                  type="number"
                  placeholder="100"
                  value={vehicleData.ladderHeight}
                  onChange={(e) =>
                    handleInputChange("ladderHeight", e.target.value)
                  }
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="insurancePolicy">Insurance Policy Number</Label>
                <Input
                  id="insurancePolicy"
                  placeholder="POL-2024-001"
                  value={vehicleData.insurancePolicy}
                  onChange={(e) =>
                    handleInputChange("insurancePolicy", e.target.value)
                  }
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Information */}
        <Card>
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
            <CardDescription>Equipment details and notes</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="equipmentDetails">Equipment Details</Label>
                <Textarea
                  id="equipmentDetails"
                  placeholder="List major equipment, tools, and special features..."
                  value={vehicleData.equipmentDetails}
                  onChange={(e) =>
                    handleInputChange("equipmentDetails", e.target.value)
                  }
                  rows={4}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="notes">Additional Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Any additional information about the vehicle..."
                  value={vehicleData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  rows={3}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end gap-4">
          <Link href="/station-manager/vehicles">
            <Button variant="outline">Cancel</Button>
          </Link>
          <Button type="submit">
            <Save className="w-4 h-4 mr-2" />
            Add Vehicle
          </Button>
        </div>
      </form>
    </div>
  );
}
