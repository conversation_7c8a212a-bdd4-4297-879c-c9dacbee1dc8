"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowLeft,
  Users,
  Clock,
  UserPlus,
  UserMinus,
  Shield,
  User,
  Phone,
  Mail,
} from "lucide-react";
import { toast } from "sonner";

export default function TeamDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const teamId = params.id;

  const [team, setTeam] = useState(null);
  const [members, setMembers] = useState([]);
  const [availablePersonnel, setAvailablePersonnel] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isAddMemberOpen, setIsAddMemberOpen] = useState(false);
  const [selectedPersonnel, setSelectedPersonnel] = useState("");

  useEffect(() => {
    fetchTeamDetails();
    fetchAvailablePersonnel();
  }, [teamId]);

  const fetchTeamDetails = async () => {
    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
      const token = localStorage.getItem("token");

      // Fetch team details
      const teamRes = await fetch(`${backendUrl}/api/teams/teams`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (teamRes.ok) {
        const teams = await teamRes.json();
        const currentTeam = teams.find((t) => t.id === parseInt(teamId));
        setTeam(currentTeam);
      }

      // Fetch team members
      const membersRes = await fetch(
        `${backendUrl}/api/teams/teams/${teamId}/members`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (membersRes.ok) {
        const membersData = await membersRes.json();
        setMembers(membersData);
      }
    } catch (error) {
      console.error("Error fetching team details:", error);
      toast.error("Failed to load team details");
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailablePersonnel = async () => {
    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
      const token = localStorage.getItem("token");

      const res = await fetch(
        `${backendUrl}/api/dashboard/station-manager/personnel`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (res.ok) {
        const data = await res.json();
        // Filter out drivers and already assigned personnel
        const responders = data.personnel.filter(
          (person) => person.role === "Responder"
        );
        setAvailablePersonnel(responders);
      }
    } catch (error) {
      console.error("Error fetching personnel:", error);
    }
  };

  const handleAddMember = async () => {
    if (!selectedPersonnel) return;

    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
      const token = localStorage.getItem("token");

      const res = await fetch(
        `${backendUrl}/api/teams/teams/${teamId}/add-member`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            user_id: selectedPersonnel,
            role: "member",
          }),
        }
      );

      if (res.ok) {
        toast.success("Member added successfully");
        setIsAddMemberOpen(false);
        setSelectedPersonnel("");
        fetchTeamDetails();
        fetchAvailablePersonnel();
      } else {
        const error = await res.json();
        toast.error(error.detail || "Failed to add member");
      }
    } catch (error) {
      console.error("Error adding member:", error);
      toast.error("Failed to add member");
    }
  };

  const handleRemoveMember = async (userId) => {
    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
      const token = localStorage.getItem("token");

      const res = await fetch(
        `${backendUrl}/api/teams/teams/${teamId}/remove-member`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            user_id: userId,
          }),
        }
      );

      if (res.ok) {
        toast.success("Member removed successfully");
        fetchTeamDetails();
        fetchAvailablePersonnel();
      } else {
        const error = await res.json();
        toast.error(error.detail || "Failed to remove member");
      }
    } catch (error) {
      console.error("Error removing member:", error);
      toast.error("Failed to remove member");
    }
  };

  const handleClockIn = async () => {
    try {
      const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || "";
      const token = localStorage.getItem("token");

      const res = await fetch(`${backendUrl}/api/teams/clock-in`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          team_id: parseInt(teamId),
        }),
      });

      if (res.ok) {
        toast.success("Team clocked in successfully");
        fetchTeamDetails();
      } else {
        const error = await res.json();
        toast.error(error.detail || "Failed to clock in team");
      }
    } catch (error) {
      console.error("Error clocking in team:", error);
      toast.error("Failed to clock in team");
    }
  };

  const getAvailablePersonnelForTeam = () => {
    const assignedUserIds = members.map((member) => member.user_id);
    return availablePersonnel.filter(
      (person) => !assignedUserIds.includes(person.id)
    );
  };

  if (loading) {
    return <div className="p-6">Loading...</div>;
  }

  if (!team) {
    return (
      <div className="p-6">
        <p>Team not found</p>
        <Button onClick={() => router.back()} className="mt-4">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{team.name}</h1>
            <p className="text-muted-foreground">Team Details & Management</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={handleClockIn} className="flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Clock In Team
          </Button>
        </div>
      </div>

      {/* Team Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{members.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">On Duty</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{team.on_duty_count || 0}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Team Status</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge variant={team.on_duty_count > 0 ? "default" : "outline"}>
              {team.on_duty_count > 0 ? "Active" : "Standby"}
            </Badge>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Station</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{team.station_id}</div>
          </CardContent>
        </Card>
      </div>

      {/* Team Members */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              Team Members
            </CardTitle>
            <Dialog open={isAddMemberOpen} onOpenChange={setIsAddMemberOpen}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <UserPlus className="w-4 h-4 mr-2" />
                  Add Member
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Team Member</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">
                      Select Personnel
                    </label>
                    <Select
                      value={selectedPersonnel}
                      onValueChange={setSelectedPersonnel}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a responder" />
                      </SelectTrigger>
                      <SelectContent>
                        {getAvailablePersonnelForTeam().map((person) => (
                          <SelectItem key={person.id} value={person.id}>
                            {person.name} ({person.email})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsAddMemberOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleAddMember}
                      disabled={!selectedPersonnel}
                    >
                      Add Member
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {members.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {members.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <User className="w-4 h-4 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{member.user.full_name}</p>
                          <p className="text-sm text-muted-foreground">
                            ID: {member.user_id.slice(0, 8)}...
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-sm">
                          <Mail className="w-3 h-3" />
                          {member.user.email}
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Phone className="w-3 h-3" />
                          {member.user.phone_number}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          member.role === "leader" ? "default" : "secondary"
                        }
                      >
                        {member.role}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          member.user.is_active ? "default" : "destructive"
                        }
                      >
                        {member.user.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRemoveMember(member.user_id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <UserMinus className="w-4 h-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                No members assigned to this team
              </p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => setIsAddMemberOpen(true)}
              >
                <UserPlus className="w-4 h-4 mr-2" />
                Add First Member
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
