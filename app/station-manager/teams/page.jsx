"use client";

import { useEffect, useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  Plus,
  Users,
  Shield,
  Clock,
  UserPlus,
  Edit,
  Trash2,
  Eye,
} from "lucide-react";
import { toast } from "sonner";
import Link from "next/link";

export default function TeamsManagement() {
  const [teams, setTeams] = useState([]);
  const [personnel, setPersonnel] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isCreateTeamOpen, setIsCreateTeamOpen] = useState(false);
  const [isAddMemberOpen, setIsAddMemberOpen] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState(null);

  const [newTeam, setNewTeam] = useState({
    name: "",
  });

  const [newMember, setNewMember] = useState({
    user_id: "",
    role: "member",
  });

  const getStationId = () => {
    if (typeof window !== "undefined") {
      const user = JSON.parse(localStorage.getItem("user") || "{}");
      return user.station_id;
    }
    return null;
  };

  useEffect(() => {
    async function fetchTeams() {
      setLoading(true);
      try {
        const backendUrl =
          process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";
        const token =
          typeof window !== "undefined" ? localStorage.getItem("token") : null;

        const res = await fetch(`${backendUrl}/api/teams/teams`, {
          headers: {
            ...(token ? { Authorization: `Bearer ${token}` } : {}),
          },
        });

        if (!res.ok) throw new Error("Failed to fetch teams");
        const data = await res.json();
        setTeams(data);
      } catch (e) {
        setTeams([]);
        toast.error("Failed to load teams");
      } finally {
        setLoading(false);
      }
    }

    async function fetchPersonnel() {
      try {
        const backendUrl =
          process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";
        const token =
          typeof window !== "undefined" ? localStorage.getItem("token") : null;

        const res = await fetch(
          `${backendUrl}/api/dashboard/station-manager/personnel`,
          {
            headers: {
              ...(token ? { Authorization: `Bearer ${token}` } : {}),
            },
          }
        );

        if (!res.ok) throw new Error("Failed to fetch personnel");
        const data = await res.json();
        setPersonnel(data.personnel.filter((p) => p.role === "responder"));
      } catch (e) {
        setPersonnel([]);
        toast.error("Failed to load personnel");
      }
    }

    fetchTeams();
    fetchPersonnel();
  }, []);

  const handleCreateTeam = async () => {
    try {
      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";
      const token =
        typeof window !== "undefined" ? localStorage.getItem("token") : null;

      const res = await fetch(`${backendUrl}/api/teams/teams`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: newTeam.name,
          station_id: getStationId(),
        }),
      });

      if (!res.ok) throw new Error("Failed to create team");
      const created = await res.json();

      setTeams((prev) => [...prev, created]);
      setIsCreateTeamOpen(false);
      setNewTeam({ name: "" });
      toast.success("Team created successfully");
    } catch (error) {
      toast.error("Failed to create team");
      console.error(error);
    }
  };

  const handleAddMember = async () => {
    if (!selectedTeam || !newMember.user_id) return;

    try {
      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";
      const token =
        typeof window !== "undefined" ? localStorage.getItem("token") : null;

      const res = await fetch(
        `${backendUrl}/api/teams/teams/${selectedTeam.id}/members`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            team_id: selectedTeam.id,
            user_id: newMember.user_id,
            role: newMember.role,
          }),
        }
      );

      if (!res.ok) throw new Error("Failed to add member");

      // Refresh teams data
      const teamsRes = await fetch(`${backendUrl}/api/teams/teams`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (teamsRes.ok) {
        const data = await teamsRes.json();
        setTeams(data);
      }

      setIsAddMemberOpen(false);
      setNewMember({ user_id: "", role: "member" });
      toast.success("Member added successfully");
    } catch (error) {
      toast.error("Failed to add member");
      console.error(error);
    }
  };

  const handleClockIn = async (teamId) => {
    try {
      const backendUrl =
        process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:8000";
      const token =
        typeof window !== "undefined" ? localStorage.getItem("token") : null;

      const res = await fetch(`${backendUrl}/api/teams/clock-in`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          team_id: teamId,
        }),
      });

      if (!res.ok) throw new Error("Failed to clock in team");

      toast.success("Team clocked in successfully");

      // Refresh teams data
      const teamsRes = await fetch(`${backendUrl}/api/teams/teams`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (teamsRes.ok) {
        const data = await teamsRes.json();
        setTeams(data);
      }
    } catch (error) {
      toast.error("Failed to clock in team");
      console.error(error);
    }
  };

  const getAvailablePersonnel = () => {
    const assignedUserIds = teams.flatMap(
      (team) => team.members?.map((member) => member.user_id) || []
    );
    return personnel.filter((person) => !assignedUserIds.includes(person.id));
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Teams Management</h1>
        <p className="text-muted-foreground">
          Manage response teams and their members
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Teams</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{teams.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">On Duty</CardTitle>
            <Shield className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {teams.filter((team) => team.on_duty_count > 0).length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Members</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {teams.reduce(
                (sum, team) => sum + (team.members?.length || 0),
                0
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Teams Management */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Teams</CardTitle>
            <Dialog open={isCreateTeamOpen} onOpenChange={setIsCreateTeamOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Team
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Team</DialogTitle>
                  <DialogDescription>
                    Create a new response team for your station
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="team-name">Team Name</Label>
                    <Input
                      id="team-name"
                      placeholder="Alpha Team"
                      value={newTeam.name}
                      onChange={(e) =>
                        setNewTeam((t) => ({ ...t, name: e.target.value }))
                      }
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateTeamOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleCreateTeam} disabled={!newTeam.name}>
                    Create Team
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Team Name</TableHead>
                <TableHead>Members</TableHead>
                <TableHead>On Duty</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {teams.map((team) => (
                <TableRow key={team.id}>
                  <TableCell className="font-medium">{team.name}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4" />
                      {team.members?.length || 0}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={team.on_duty_count > 0 ? "default" : "secondary"}
                    >
                      {team.on_duty_count || 0} on duty
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={team.on_duty_count > 0 ? "default" : "outline"}
                    >
                      {team.on_duty_count > 0 ? "Active" : "Standby"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Link href={`/station-manager/teams/${team.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="w-4 h-4 mr-1" />
                          View
                        </Button>
                      </Link>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleClockIn(team.id)}
                      >
                        <Clock className="w-4 h-4 mr-1" />
                        Clock In
                      </Button>
                      <Dialog
                        open={isAddMemberOpen && selectedTeam?.id === team.id}
                        onOpenChange={(open) => {
                          setIsAddMemberOpen(open);
                          if (open) setSelectedTeam(team);
                          else setSelectedTeam(null);
                        }}
                      >
                        <DialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            <UserPlus className="w-4 h-4 mr-1" />
                            Add Member
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Add Team Member</DialogTitle>
                            <DialogDescription>
                              Add a responder to {team.name}
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div className="space-y-2">
                              <Label htmlFor="member-select">
                                Select Responder
                              </Label>
                              <Select
                                value={newMember.user_id}
                                onValueChange={(val) =>
                                  setNewMember((m) => ({ ...m, user_id: val }))
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select responder" />
                                </SelectTrigger>
                                <SelectContent>
                                  {getAvailablePersonnel().map((person) => (
                                    <SelectItem
                                      key={person.id}
                                      value={person.id}
                                    >
                                      {person.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="role-select">Role</Label>
                              <Select
                                value={newMember.role}
                                onValueChange={(val) =>
                                  setNewMember((m) => ({ ...m, role: val }))
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="leader">
                                    Team Leader
                                  </SelectItem>
                                  <SelectItem value="member">Member</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                          <DialogFooter>
                            <Button
                              variant="outline"
                              onClick={() => setIsAddMemberOpen(false)}
                            >
                              Cancel
                            </Button>
                            <Button
                              onClick={handleAddMember}
                              disabled={!newMember.user_id}
                            >
                              Add Member
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
