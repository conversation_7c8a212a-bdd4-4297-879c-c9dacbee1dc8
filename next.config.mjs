/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    BACKEND_URL: "http://localhost:8000",
    NEXT_PUBLIC_BACKEND_URL: "http://localhost:8000",
    // BACKEND_URL: "https://my-guardian-plus-1.onrender.com",
    // NEXT_PUBLIC_BACKEND_URL: "https://my-guardian-plus-1.onrender.com",
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
};

export default nextConfig;
